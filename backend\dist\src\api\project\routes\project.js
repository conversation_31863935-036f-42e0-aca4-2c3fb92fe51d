"use strict";
/**
 * project router
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.customRoutes = void 0;
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreRouter('api::project.project');
// Custom routes
exports.customRoutes = {
    routes: [
        {
            method: 'GET',
            path: '/projects/:id/properties',
            handler: 'project.getProperties',
            config: {
                policies: [],
                middlewares: [],
            },
        },
    ],
};
