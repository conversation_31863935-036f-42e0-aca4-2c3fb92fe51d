/**
 * property controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::property.property', ({ strapi }) => ({
  // Custom find method to include owner and agent information
  async find(ctx) {
    try {
      // Use entityService directly to avoid super.find issues
      const { query } = ctx;
      const properties = await strapi.entityService.findMany('api::property.property', {
        ...query,
        populate: {
          owner: {
            fields: ['id', 'username', 'email', 'firstName', 'lastName', 'phone', 'company']
          },
          agent: {
            fields: ['id', 'username', 'email', 'firstName', 'lastName', 'phone', 'company']
          },
          images: true,
          floorPlan: true,
          project: {
            fields: ['id', 'name', 'slug']
          }
        }
      });

      return { data: properties };
    } catch (error) {
      console.error('Error in property find:', error);
      return { data: [], meta: { pagination: { total: 0 } } };
    }
  },

  // Custom findOne method
  async findOne(ctx) {
    const { id } = ctx.params;

    const property = await strapi.entityService.findOne(
      'api::property.property',
      id,
      {
        populate: ['owner', 'agent', 'images', 'floorPlan', 'project']
      }
    ) as any;

    if (!property) {
      return ctx.notFound('Property not found');
    }

    // Increment view count
    await strapi.entityService.update('api::property.property', id, {
      data: {
        views: (property.views || 0) + 1
      }
    });

    return { data: property };
  },

  // Get property for editing (without incrementing view count)
  async getForEdit(ctx) {
    const { id } = ctx.params;
    const user = ctx.state.user;

    console.log('getForEdit called with ID:', id);
    console.log('User:', user ? user.id : 'No user');

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    try {
      // Try to find by documentId first (Strapi v5), then by numeric ID
      let property;

      console.log('Trying to find property with ID:', id, 'Type:', typeof id);

      // Check if ID looks like a documentId (long string) or numeric ID
      const isNumericId = /^\d+$/.test(id);
      console.log('Is numeric ID:', isNumericId);

      if (!isNumericId) {
        // Try with documentId using findOne directly (this should work in Strapi v5)
        try {
          console.log('Attempting documentId lookup with findOne...');
          property = await strapi.entityService.findOne(
            'api::property.property',
            id,
            {
              populate: ['owner', 'agent', 'images', 'floorPlan', 'project']
            }
          ) as any;
          console.log('Found property with documentId:', property ? `${property.id} (${property.documentId})` : 'Not found');
        } catch (error) {
          console.log('DocumentId search failed:', error.message);
          // If documentId fails, try to find all properties and match documentId manually
          try {
            console.log('Trying manual documentId matching...');
            const allProperties = await strapi.entityService.findMany(
              'api::property.property',
              {
                populate: ['owner', 'agent', 'images', 'floorPlan', 'project']
              }
            ) as any[];

            property = allProperties.find(p => p.documentId === id) || null;
            console.log('Manual documentId match result:', property ? `${property.id} (${property.documentId})` : 'Not found');
          } catch (manualError) {
            console.log('Manual documentId matching failed:', manualError.message);
            property = null;
          }
        }
      } else {
        // Try with numeric ID
        try {
          console.log('Attempting numeric ID lookup...');
          const properties = await strapi.entityService.findMany(
            'api::property.property',
            {
              filters: { id: parseInt(id) },
              populate: ['owner', 'agent', 'images', 'floorPlan', 'project']
            }
          ) as any[];

          property = properties.length > 0 ? properties[0] : null;
          console.log('Found property with numeric ID:', property ? `${property.id} (${property.documentId})` : 'Not found');
        } catch (numericError) {
          console.log('Numeric ID search failed:', numericError.message);
          property = null;
        }
      }

      if (!property) {
        console.log('Property not found for ID:', id);
        return ctx.notFound('Property not found');
      }

      // Check if user is owner or admin
      const ownerId = property.owner?.id || property.owner;
      console.log('Property owner ID:', ownerId, 'User ID:', user.id);

      if (ownerId !== user.id && user.role?.name !== 'Admin') {
        console.log('Access denied - user is not owner or admin');
        return ctx.forbidden('You can only edit your own properties');
      }

      console.log('Returning property data for editing');
      // Return property without incrementing view count
      return { data: property };
    } catch (error) {
      console.error('Error in getForEdit:', error);
      return ctx.internalServerError('Failed to fetch property for editing');
    }
  },

  // Custom create method to set owner
  async create(ctx) {
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in to create a property');
    }

    try {
      // Check membership limits
      const membershipService = strapi.service('api::membership.membership');
      const canCreate = await membershipService.canCreateProperty(user.id);

      if (!canCreate) {
        return ctx.forbidden('You have reached your property limit. Please upgrade your membership.');
      }

      // Create property using entityService directly
      const propertyData = {
        ...ctx.request.body.data,
        owner: user.id
      };

      const property = await strapi.entityService.create('api::property.property', {
        data: propertyData,
        populate: {
          owner: {
            fields: ['id', 'username', 'email']
          }
        }
      });

      return { data: property };
    } catch (error) {
      console.error('Error creating property:', error);
      console.error('Error stack:', error.stack);
      console.error('Error message:', error.message);
      console.error('User ID:', user.id);
      console.error('Request body:', JSON.stringify(ctx.request.body, null, 2));
      return ctx.internalServerError(`Failed to create property: ${error.message}`);
    }
  },

  // Custom update method to check ownership
  async update(ctx) {
    const { id } = ctx.params;
    const user = ctx.state.user;

    console.log('Update called with ID:', id);
    console.log('User:', user ? user.id : 'No user');

    if (!user) {
      return ctx.unauthorized('You must be logged in to update a property');
    }

    try {
      // Try to find by documentId first (Strapi v5), then by numeric ID
      let property;
      let actualId = id; // The ID we'll use for the update

      console.log('Trying to find property with ID:', id);

      // Check if ID looks like a documentId (long string) or numeric ID
      const isNumericId = /^\d+$/.test(id);
      console.log('Is numeric ID:', isNumericId);

      if (!isNumericId) {
        // Try with documentId using findOne directly
        try {
          console.log('Attempting documentId lookup with findOne...');
          property = await strapi.entityService.findOne(
            'api::property.property',
            id,
            {
              populate: ['owner']
            }
          ) as any;
          console.log('Found property with documentId:', property ? `${property.id} (${property.documentId})` : 'Not found');
          if (property) {
            actualId = property.documentId; // Use documentId for update
          }
        } catch (error) {
          console.log('DocumentId search failed:', error.message);
          // If documentId fails, try to find all properties and match documentId manually
          try {
            console.log('Trying manual documentId matching...');
            const allProperties = await strapi.entityService.findMany(
              'api::property.property',
              {
                populate: ['owner']
              }
            ) as any[];

            property = allProperties.find(p => p.documentId === id) || null;
            console.log('Manual documentId match result:', property ? `${property.id} (${property.documentId})` : 'Not found');
            if (property) {
              actualId = property.documentId; // Use documentId for update
            }
          } catch (manualError) {
            console.log('Manual documentId matching failed:', manualError.message);
            property = null;
          }
        }
      } else {
        // Try with numeric ID
        try {
          console.log('Attempting numeric ID lookup...');
          const properties = await strapi.entityService.findMany(
            'api::property.property',
            {
              filters: { id: parseInt(id) },
              populate: ['owner']
            }
          ) as any[];

          property = properties.length > 0 ? properties[0] : null;
          console.log('Found property with numeric ID:', property ? `${property.id} (${property.documentId})` : 'Not found');
          if (property) {
            actualId = property.documentId; // Use documentId for update
          }
        } catch (numericError) {
          console.log('Numeric ID search failed:', numericError.message);
          property = null;
        }
      }

      if (!property) {
        console.log('Property not found for ID:', id);
        return ctx.notFound('Property not found');
      }

      // Check if user is owner or admin
      const ownerId = property.owner?.id || property.owner;
      console.log('Property owner ID:', ownerId, 'User ID:', user.id);

      if (ownerId !== user.id && user.role?.name !== 'Admin') {
        console.log('Access denied - user is not owner or admin');
        return ctx.forbidden('You can only update your own properties');
      }

      console.log('Updating property with actualId:', actualId);

      // Update using entityService directly with the correct ID
      const updatedProperty = await strapi.entityService.update(
        'api::property.property',
        actualId,
        {
          data: ctx.request.body.data,
          populate: ['owner', 'agent', 'images', 'floorPlan', 'project']
        }
      );

      console.log('Property updated successfully');
      return { data: updatedProperty };
    } catch (error) {
      console.error('Error in property update:', error);
      return ctx.internalServerError('Failed to update property');
    }
  },

  // Custom delete method to check ownership
  async delete(ctx) {
    const { id } = ctx.params;
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in to delete a property');
    }

    const property = await strapi.entityService.findOne('api::property.property', id, {
      populate: ['owner']
    }) as any;

    if (!property) {
      return ctx.notFound('Property not found');
    }

    // Check if user is owner or admin
    // @ts-ignore
    const ownerId = property.owner?.id || property.owner;
    if (ownerId !== user.id && user.role?.name !== 'Admin') {
      return ctx.forbidden('You can only delete your own properties');
    }

    const response = await super.delete(ctx);
    return response;
  },

  // Get properties by user
  async getMyProperties(ctx) {
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    try {
      const properties = await strapi.entityService.findMany('api::property.property', {
        filters: { owner: user.id },
        populate: {
          images: true,
          project: {
            fields: ['id', 'name', 'slug']
          }
        }
      });

      return { data: properties };
    } catch (error) {
      console.error('Error fetching user properties:', error);
      return ctx.internalServerError('Failed to fetch properties');
    }
  },

  // Get featured properties
  async getFeatured(ctx) {
    try {
      const properties = await strapi.entityService.findMany(
        'api::property.property',
        {
          filters: {
            featured: true,
            publishedAt: { $notNull: true },
          },
          populate: ['images', 'floorPlan', 'owner', 'agent'],
          sort: { createdAt: 'desc' },
          pagination: {
            limit: 6,
          },
        }
      );

      return { data: properties };
    } catch (error) {
      console.error('Error fetching featured properties:', error);
      return ctx.internalServerError('Failed to fetch featured properties');
    }
  },

  // Publish property
  async publish(ctx) {
    const { id } = ctx.params;
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    try {
      const property = await strapi.entityService.findOne('api::property.property', id, {
        populate: ['owner']
      }) as any;

      if (!property) {
        return ctx.notFound('Property not found');
      }

      // Check if user is owner or admin
      const ownerId = property.owner?.id || property.owner;
      if (ownerId !== user.id && user.role?.name !== 'Admin') {
        return ctx.forbidden('You can only publish your own properties');
      }

      const updatedProperty = await strapi.entityService.update('api::property.property', id, {
        data: {
          publishedAt: new Date()
        }
      });

      return { data: updatedProperty };
    } catch (error) {
      console.error('Error publishing property:', error);
      return ctx.internalServerError('Failed to publish property');
    }
  },

  // Unpublish property
  async unpublish(ctx) {
    const { id } = ctx.params;
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    try {
      const property = await strapi.entityService.findOne('api::property.property', id, {
        populate: ['owner']
      }) as any;

      if (!property) {
        return ctx.notFound('Property not found');
      }

      // Check if user is owner or admin
      const ownerId = property.owner?.id || property.owner;
      if (ownerId !== user.id && user.role?.name !== 'Admin') {
        return ctx.forbidden('You can only unpublish your own properties');
      }

      const updatedProperty = await strapi.entityService.update('api::property.property', id, {
        data: {
          publishedAt: null
        }
      });

      return { data: updatedProperty };
    } catch (error) {
      console.error('Error unpublishing property:', error);
      return ctx.internalServerError('Failed to unpublish property');
    }
  },

  // Generate nearby places for a property
  async generateNearbyPlaces(ctx) {
    const { id } = ctx.params;
    const user = ctx.state.user;

    if (!user) {
      return ctx.unauthorized('You must be logged in');
    }

    try {
      const property = await strapi.entityService.findOne('api::property.property', id, {
        populate: ['owner']
      }) as any;

      if (!property) {
        return ctx.notFound('Property not found');
      }

      // Check if user is owner or admin
      const ownerId = property.owner?.id || property.owner;
      if (ownerId !== user.id && user.role?.name !== 'Admin') {
        return ctx.forbidden('You can only generate nearby places for your own properties');
      }

      // Check if property has coordinates
      if (!property.coordinates || !property.coordinates.lat || !property.coordinates.lng) {
        return ctx.badRequest('Property must have coordinates to generate nearby places');
      }

      // Get enabled place categories
      const categories = await strapi.entityService.findMany('api::nearby-place-category.nearby-place-category', {
        filters: { enabled: true },
        sort: { priority: 'desc' }
      });

      if (categories.length === 0) {
        return ctx.badRequest('No place categories are enabled');
      }

      // Generate nearby places for each category
      const propertyService = strapi.service('api::property.property');
      const nearbyPlaces = {};

      for (const category of categories) {
        try {
          const places = await propertyService.findNearbyPlaces({
            lat: property.coordinates.lat,
            lng: property.coordinates.lng,
            types: category.googlePlaceTypes,
            radius: category.searchRadius,
            maxResults: category.maxResults
          });

          nearbyPlaces[category.name] = {
            category: {
              id: category.id,
              name: category.name,
              displayName: category.displayName,
              icon: category.icon,
              color: category.color
            },
            places: places
          };
        } catch (error) {
          console.error(`Error fetching places for category ${category.name}:`, error.message);
          nearbyPlaces[category.name] = {
            category: {
              id: category.id,
              name: category.name,
              displayName: category.displayName,
              icon: category.icon,
              color: category.color
            },
            places: [],
            error: error.message
          };
        }
      }

      // Update property with nearby places
      const updatedProperty = await strapi.entityService.update('api::property.property', id, {
        data: {
          nearbyPlaces: nearbyPlaces
        }
      });

      return { data: { nearbyPlaces: nearbyPlaces } };
    } catch (error) {
      console.error('Error generating nearby places:', error);
      return ctx.internalServerError('Failed to generate nearby places');
    }
  },

  // Get nearby places for a property
  async getNearbyPlaces(ctx) {
    const { id } = ctx.params;

    try {
      const property = await strapi.entityService.findOne('api::property.property', id, {
        fields: ['nearbyPlaces']
      }) as any;

      if (!property) {
        return ctx.notFound('Property not found');
      }

      return { data: property.nearbyPlaces || {} };
    } catch (error) {
      console.error('Error fetching nearby places:', error);
      return ctx.internalServerError('Failed to fetch nearby places');
    }
  },

  // Find nearby places for given coordinates (used by frontend map preview)
  async findNearbyPlaces(ctx) {


    try {
      const { lat, lng, types, radius = 1500, maxResults = 10 } = ctx.request.body;

      // Validate required parameters
      if (!lat || !lng) {
        return ctx.badRequest('Latitude and longitude are required');
      }

      if (!types || !Array.isArray(types) || types.length === 0) {
        return ctx.badRequest('Place types array is required');
      }

      // Use the property service with Google Places functionality
      const propertyService = strapi.service('api::property.property');

      const places = await propertyService.findNearbyPlaces({
        lat: parseFloat(lat),
        lng: parseFloat(lng),
        types,
        radius: parseInt(radius),
        maxResults: parseInt(maxResults)
      });

      return { data: places };
    } catch (error) {
      console.error('Error finding nearby places:', error.message);
      return ctx.internalServerError(`Failed to find nearby places: ${error.message}`);
    }
  }
}));
