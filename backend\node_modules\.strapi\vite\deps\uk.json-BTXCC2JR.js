import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/translations/uk.json.mjs
var groups = "Групи";
var models = "Типи Колекцій";
var pageNotFound = "Сторінка не знайдена";
var uk = {
  "App.schemas.data-loaded": "Схеми було успішно завантажено",
  "actions.clone.error": "Під час спроби клонувати документ сталася помилка.",
  "actions.clone.label": "Дублювати",
  "actions.delete.dialog.body": "Ви впевнені, що хочете видалити цей документ? Ця дія незворотна.",
  "actions.delete.error": "Під час спроби видалити документ сталася помилка.",
  "actions.delete.label": "Видалити запис{isLocalized, select, true { (всі локалі)} other {}}",
  "actions.discard.label": "Відхилити зміни",
  "actions.discard.dialog.body": "Ви впевнені, що хочете відхилити зміни? Ця дія незворотна.",
  "actions.edit.error": "Під час спроби редагувати документ сталася помилка.",
  "actions.edit.label": "Редагувати",
  "actions.unpublish.error": "Під час спроби скасувати публікацію документа сталася помилка.",
  "actions.unpublish.dialog.body": "Ви впевнені, що хочете скасувати публікацію цього?",
  "actions.unpublish.dialog.option.keep-draft": "Скасувати публікацію та зберегти останню чернетку",
  "actions.unpublish.dialog.option.replace-draft": "Скасувати публікацію та замінити останню чернетку",
  "ListViewTable.relation-loaded": "Зв'язки були завантажені",
  "ListViewTable.relation-loading": "Зв'язки завантажуються",
  "ListViewTable.relation-more": "Цей зв'язок містить більше сутностей, ніж відображено",
  "EditRelations.title": "Зв'язок",
  "HeaderLayout.button.label-add-entry": "Додати запис",
  "api.id": "API ID",
  "apiError.This attribute must be unique": "Поле {field} повинно бути унікальним",
  "components.AddFilterCTA.add": "Фільтри",
  "components.AddFilterCTA.hide": "Фільтри",
  "components.DragHandle-label": "Перетягніть для зміни порядку",
  "components.DraggableAttr.edit": "Натисніть щоб змінити",
  "components.DraggableCard.delete.field": "Видалити {item}",
  "components.DraggableCard.edit.field": "Редагувати {item}",
  "components.DraggableCard.move.field": "Перемістити {item}",
  "components.ListViewTable.row-line": "рядок елемента {number}",
  "components.DynamicZone.ComponentPicker-label": "Виберіть один компонент",
  "components.DynamicZone.add-component": "Додати компонент до {componentName}",
  "components.DynamicZone.delete-label": "Видалити {name}",
  "components.DynamicZone.error-message": "Компонент містить помилки",
  "components.DynamicZone.missing-components": "Є {number, plural, =0 {# відсутніх компонентів} one {# відсутній компонент} other {# відсутніх компонентів}}",
  "components.DynamicZone.extra-components": "Є {number, plural, =0 {# зайвих компонентів} one {# зайвий компонент} other {# зайвих компонентів}}",
  "components.DynamicZone.move-down-label": "Перемістити компонент вниз",
  "components.DynamicZone.move-up-label": "Перемістити компонент вгору",
  "components.DynamicZone.pick-compo": "Виберіть один компонент",
  "components.DynamicZone.required": "Компонент є обов'язковим",
  "components.EmptyAttributesBlock.button": "Перейти до налаштувань",
  "components.EmptyAttributesBlock.description": "Ви можете змінити свої налаштування",
  "components.FieldItem.linkToComponentLayout": "Встановити макет компоненту",
  "components.FieldSelect.label": "Додати поле",
  "components.FilterOptions.button.apply": "Застосувати",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Застосувати",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Очистити все",
  "components.FiltersPickWrapper.PluginHeader.description": "Вкажіть умови фільтрації записів",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Фільтри",
  "components.FiltersPickWrapper.hide": "Сховати",
  "components.Filters.usersSelect.label": "Пошук та вибір користувача для фільтрації",
  "components.LeftMenu.Search.label": "Пошук типу вмісту",
  "components.LeftMenu.collection-types": "Типи колекцій",
  "components.LeftMenu.single-types": "Типи одиниць",
  "components.LimitSelect.itemsPerPage": "Елементів на сторінку",
  "components.NotAllowedInput.text": "Вам не дозволено переглядати це поле",
  "components.RelationInput.icon-button-aria-label": "Перетягнути",
  "components.RepeatableComponent.error-message": "Компоненти містять помилки",
  "components.Search.placeholder": "Пошук записів...",
  "components.Select.draft-info-title": "Чернетка",
  "components.Select.publish-info-title": "Опубліковано",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "Налаштуйте, як буде виглядати екран редагування.",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "Визначте параметри вигяду списку.",
  "components.SettingsViewWrapper.pluginHeader.title": "Налаштуйте вигляд - {name}",
  "bulk-publish.already-published": "Вже опубліковано",
  "bulk-unpublish.already-unpublished": "Вже скасовано публікацію",
  "bulk-publish.modified": "Готово до публікації змін",
  "components.TableDelete.delete": "Видалити все",
  "components.TableDelete.deleteSelected": "Видалити обране",
  "components.TableDelete.label": "Вибрано {number, plural, one {# запис} other {# записів}} selected",
  "components.TableEmpty.withFilters": "Немає {contentType} з обраними фільтрами...",
  "components.TableEmpty.withSearch": 'Немає {contentType}, які відповідають пошуку "{search}"...',
  "components.TableEmpty.withoutFilter": "Немає {contentType}...",
  "components.empty-repeatable": "Немає записів. Натисніть кнопку нижче, щоб додати нову.",
  "components.notification.info.maximum-requirement": "Ви досягли максимальної кількості полей",
  "components.notification.info.minimum-requirement": "Поле було додано, щоб відповідати мінімальним вимогам",
  "components.repeatable.reorder.error": "Сталася помилка під час зміни порядку поля вашого компонента, будь ласка, спробуйте ще раз",
  "components.reset-entry": "Скинути запис",
  "components.uid.apply": "Вибрати",
  "components.uid.available": "Доступний",
  "components.uid.regenerate": "Згенерувати",
  "components.uid.suggested": "Рекомендоване",
  "components.uid.unavailable": "Недоступний",
  "containers.edit.tabs.label": "Статус документа",
  "containers.edit.tabs.draft": "чернетка",
  "containers.edit.tabs.published": "опубліковано",
  "containers.edit.panels.default.title": "Запис",
  "containers.edit.panels.default.more-actions": "Більше дій з документом",
  "containers.Edit.delete": "Видалити",
  "containers.edit.title.new": "Створити запис",
  "containers.edit.header.more-actions": "Більше дій",
  "containers.edit.information.last-published.label": "Опубліковано",
  "containers.edit.information.last-published.value": "{time}{isAnonymous, select, true {} other { автор: {author}}}",
  "containers.edit.information.last-draft.label": "Оновлено",
  "containers.edit.information.last-draft.value": "{time}{isAnonymous, select, true {} other { автор: {author}}}",
  "containers.edit.information.document.label": "Створено",
  "containers.edit.information.document.value": "{time}{isAnonymous, select, true {} other { автор: {author}}}",
  "containers.EditSettingsView.modal-form.edit-field": "Налаштуйте поле",
  "containers.EditView.add.new-entry": "Add an entry",
  "containers.EditView.notification.errors": "Форма містить деякі помилки",
  "containers.Home.introduction": "Щоб редагувати ваші записи, перейдіть за посиланням в лівому меню. Цей плаґін не має належного способу редагування налаштувань і все ще активно розробляється.",
  "containers.Home.pluginHeaderDescription": "Керуйте своїми записами за допомогою потужного та красивого інтерфейсу.",
  "containers.Home.pluginHeaderTitle": "Контент-менеджер",
  "containers.List.draft": "Чернетка",
  "containers.List.published": "Опубліковано",
  "containers.List.modified": "Змінено",
  "containers.list.displayedFields": "Показувати поля",
  "containers.list.items": "{number} {number, plural, =0 {елементів} one {елемент} other {елементів}}",
  "containers.list.table.row-actions": "Дії рядка",
  "containers.list.selectedEntriesModal.title": "Опублікувати записи",
  "containers.list.selectedEntriesModal.selectedCount.publish": "<b>{publishedCount}</b> {publishedCount, plural, =0 {записів} one {запис} other {записів}} вже опубліковано. <b>{draftCount}</b> {draftCount, plural, =0 {записів} one {запис} other {записів}} готові до публікації. <b>{withErrorsCount}</b> {withErrorsCount, plural, =0 {записів} one {запис} other {записів}} чекають дій.",
  "containers.list.selectedEntriesModal.selectedCount.unpublish": "<b>{draftCount}</b> {draftCount, plural, =0 {записів} one {запис} other {записів}} вже скасовано публікацію. <b>{publishedCount}</b> {publishedCount, plural, =0 {записів} one {запис} other {записів}} готові до скасування публікації.",
  "containers.list.autoCloneModal.header": "Дублювати",
  "containers.list.autoCloneModal.title": "Цей запис не може бути дубльований безпосередньо.",
  "containers.list.autoCloneModal.description": "Новий запис буде створено з тим самим вмістом, але вам доведеться змінити наступні поля, щоб зберегти його.",
  "containers.list.autoCloneModal.create": "Створити",
  "containers.list.autoCloneModal.error.unique": "Ідентичні значення в унікальному полі не дозволені.",
  "containers.list.autoCloneModal.error.relation": "Дублювання зв'язку може видалити його з оригінального запису.",
  "containers.list-settings.modal-form.label": "Редагувати {fieldName}",
  "containers.list-settings.modal-form.error": "Сталася помилка під час спроби відкрити форму.",
  "containers.edit-settings.modal-form.error": "Сталася помилка під час спроби відкрити форму.",
  "containers.edit-settings.modal-form.label": "Мітка",
  "containers.edit-settings.modal-form.description": "Опис",
  "containers.edit-settings.modal-form.placeholder": "Заповнювач",
  "containers.edit-settings.modal-form.mainField": "Назва запису",
  "containers.edit-settings.modal-form.mainField.hint": "Встановіть відображуване поле як у режимі редагування, так і в списку.",
  "containers.edit-settings.modal-form.editable": "Редаговане поле",
  "containers.edit-settings.modal-form.size": "Розмір",
  "containers.SettingPage.add.field": "Додати ще одне поле",
  "containers.SettingPage.add.relational-field": "Вставте ще одне пов’язане поле",
  "containers.SettingPage.attributes": "Поля атрибутів",
  "containers.SettingPage.attributes.description": "Визначте порядок атрибутів",
  "containers.SettingPage.editSettings.description": "Перетягніть поля щоб налаштувати вигляд.",
  "containers.SettingPage.editSettings.entry.title": "Заголовок запису",
  "containers.SettingPage.editSettings.entry.title.description": "Встановіть поле, яке буде відображати запис.",
  "containers.SettingPage.editSettings.relation-field.description": "Встановіть відображене поле як у переглядах редагування, так і в списку",
  "containers.SettingPage.editSettings.title": "Змінити вигляд (налаштування)",
  "containers.SettingPage.layout": "Компонування",
  "containers.SettingPage.listSettings.description": "Налаштуйте параметри для цього Типу Колекцій",
  "containers.SettingPage.listSettings.title": "Список (налаштування)",
  "containers.SettingPage.pluginHeaderDescription": "Налаштуйте конкретні параметри для цього Типу Колекцій",
  "containers.SettingPage.relations": "Пов’язані поля",
  "containers.SettingPage.settings": "Налаштування",
  "containers.SettingPage.view": "Вигляд",
  "containers.SettingViewModel.pluginHeader.title": "Контент-менеджер - {name}",
  "containers.SettingsPage.Block.contentType.description": "Налаштуйте конкретні параметри",
  "containers.SettingsPage.Block.contentType.title": "Типи Колекцій",
  "containers.SettingsPage.Block.generalSettings.description": "Налаштуйте параметри за замовчуванням для своїх Типи Колекцій",
  "containers.SettingsPage.Block.generalSettings.title": "Загальне",
  "containers.SettingsPage.pluginHeaderDescription": "Налаштуйте параметри для всіх ваших Типи Колекцій та груп",
  "containers.SettingsView.list.subtitle": "Налаштуйте компонування та відображення ваших Типи Колекцій та груп",
  "containers.SettingsView.list.title": "Налаштування відображення",
  "dnd.cancel-item": "{item}, відпущено. Перестановка скасована.",
  "dnd.drop-item": "{item}, відпущено. Кінцева позиція в списку: {position}.",
  "dnd.grab-item": "{item}, взято. Поточна позиція в списку: {position}. Натисніть стрілки вгору та вниз, щоб змінити положення, пробіл для відпускання, Esc для скасування.",
  "dnd.instructions": "Натисніть пробіл, щоб взяти та переставити.",
  "dnd.reorder": "{item}, переміщено. Нова позиція в списку: {position}.",
  "edit-settings-view.link-to-ctb.components": "Редагувати компонент",
  "edit-settings-view.link-to-ctb.content-types": "Редагувати тип вмісту",
  "emptyAttributes.button": "Перейдіть до конструктора Типу Колекцій",
  "emptyAttributes.description": "Додайте перше поле в ваш Тип Колекцій",
  "emptyAttributes.title": "Поки що немає полей",
  "error.attribute.key.taken": "Значення вже існує",
  "error.attribute.sameKeyAndName": "Не може співпадати",
  "error.attribute.taken": "Це поле вже існує",
  "error.contentTypeName.taken": "Ця назва вже існує",
  "error.model.fetch": "Під час завантаження конфігурації моделей сталася помилка.",
  "error.record.create": "Під час створення запису сталася помилка.",
  "error.record.delete": "Під час видалення запису сталася помилка.",
  "error.record.fetch": "Під час завантаження запису сталася помилка.",
  "error.record.update": "Під час оновлення запису сталася помилка.",
  "error.records.count": "Під час завантаження кількості записів сталася помилка.",
  "error.records.fetch": "Під час завантаження записів сталася помилка.",
  "error.records.fetch-draft-relatons": "Помилка сталася під час отримання проектів зв’язків на цьому документі.",
  "error.schema.generation": "Під час створення схеми сталася помилка.",
  "error.validation.json": "Це не відпоідає формату JSON",
  "error.validation.max": "Значення занадто велике.",
  "error.validation.maxLength": "Значення занадто довге.",
  "error.validation.min": "Значення занадто мале.",
  "error.validation.minLength": "Значення занадто коротке.",
  "error.validation.minSupMax": "Не може бути більше",
  "error.validation.regex": "Значення не відповідає регулярному виразу.",
  "error.validation.required": "Це обов'язкове поле.",
  "form.Input.bulkActions": "Дозволити масові дії",
  "form.Input.defaultSort": "Сортування за замовчуванням",
  "form.Input.description": "Опис",
  "form.Input.description.placeholder": "Ім'я, для відображення в профілі",
  "form.Input.editable": "Редагуєме поле",
  "form.Input.filters": "Увімкнути фільтри",
  "form.Input.hint.character.unit": "{maxValue, plural, one { символи} other { символів}}",
  "form.Input.hint.minMaxDivider": " / ",
  "form.Input.hint.text": "{min, select, undefined {} other {min. {min}}}{divider}{max, select, undefined {} other {max. {max}}}{unit}{br}{description}",
  "form.Input.label": "Підпис",
  "form.Input.label.inputDescription": "Це значення змінить підпис, який відображується у заголовку таблиці",
  "form.Input.pageEntries": "записів на сторінці",
  "form.Input.pageEntries.inputDescription": "Зауважте, що ви можете змінити це значення на сторінці налаштувань Типу Колекцій.",
  "form.Input.placeholder": "Плейсхолдер",
  "form.Input.placeholder.placeholder": "Моє значення",
  "form.Input.search": "Увімкнути пошук",
  "form.Input.search.field": "Дозволити шукати за цим полем",
  "form.Input.sort.field": "Дозволити сортувати за цим полем",
  "form.Input.sort.order": "Порядок сортування за замовчуванням",
  "form.Input.wysiwyg": "Відображувати як WYSIWYG",
  "global.displayedFields": "Відображені поля",
  groups,
  "groups.numbered": "Групи ({number})",
  "header.name": "Менеджер контенту",
  "link-to-ctb": "Відредагуйте модель",
  models,
  "models.numbered": "Типи Колекцій ({number})",
  "notification.error.displayedFields": "Потрібне хоча б одне поле для відображення.",
  "notification.error.relationship.fetch": "Під час завантаження зв'язків сталася помилка.",
  "notification.info.SettingPage.disableSort": "Потріен хоча б один атрібут дозволений для сортування.",
  "notification.info.minimumFields": "Необхідно відобразити хоча б одне поле.",
  "notification.upload.error": "Під час завантаження файлів сталася помилка.",
  pageNotFound,
  "pages.ListView.header-subtitle": "Знайдено {number, plural, =0 {# записів} one {# запис} other {# записів}}",
  "pages.NoContentType.button": "Створити ваш перший Тип Вмісту",
  "pages.NoContentType.text": "У вас ще немає вмісту, ми рекомендуємо створити ваш перший Тип Вмісту.",
  "permissions.not-allowed.create": "Вам не дозволено створювати документ",
  "permissions.not-allowed.update": "Вам не дозволено переглядати цей документ",
  "plugin.description.long": "Швидкий спосіб перегляду, редагування та видалення даних у вашій базі даних.",
  "plugin.description.short": "Швидкий спосіб перегляду, редагування та видалення даних у вашій базі даних.",
  "popUpWarning.bodyMessage.contentType.delete": "Ви впевнені, що хочете видалити цей запис?",
  "popUpWarning.bodyMessage.contentType.delete.all": "Ви впевнені, що хочете видалити ці записи?",
  "popUpWarning.bodyMessage.contentType.publish.all": "Ви впевнені, що хочете опублікувати ці записи?",
  "popUpWarning.bodyMessage.contentType.unpublish.all": "Ви впевнені, що хочете скасувати публікацію цих записів?",
  "popUpWarning.warning.has-draft-relations.title": "Підтвердження",
  "popUpWarning.warning.has-draft-relations.message": "Цей запис пов'язаний з {count, plural, one {# чернеткою} other {# чернетками}}. Опублікування може призвести до зламаних посилань у вашому застосунку.",
  "popUpwarning.warning.has-draft-relations.button-confirm": "Так, опублікувати",
  "popUpwarning.warning.bulk-has-draft-relations.message": "<b>{count} {count, plural, one {зв'язок} other {зв'язки}} з {entities} {entities, plural, one {запису} other {записів}} {count, plural, one {є} other {є}}</b> ще не опубліковані й можуть призвести до непередбачуваної поведінки.",
  "popUpWarning.warning.publish-question": "Ви все ще хочете опублікувати?",
  "popUpWarning.warning.unpublish": "Якщо ви не опублікуєте цей вміст, він автоматично стане чернеткою.",
  "popUpWarning.warning.unpublish-question": "Ви впевнені, що не хочете опублікувати цього?",
  "popUpWarning.warning.updateAllSettings": "Це змінить всі ваші налаштування",
  "popover.display-relations.label": "Показати зв'язки",
  "preview.panel.title": "Попередній перегляд",
  "preview.panel.button": "Відкрити попередній перегляд",
  "preview.page-title": "{contentType} попередній перегляд",
  "preview.header.close": "Закрити попередній перегляд",
  "preview.copy.label": "Скопіювати посилання на попередній перегляд",
  "preview.copy.success": "Посилання на попередній перегляд скопійовано",
  "preview.tabs.label": "Статус попереднього перегляду",
  "relation.add": "Додати зв'язок",
  "relation.disconnect": "Від'єднати",
  "relation.error-adding-relation": "Сталася помилка при спробі додати зв'язок.",
  "relation.isLoading": "Зв'язки завантажуються",
  "relation.loadMore": "Завантажити більше",
  "relation.notAvailable": "Немає доступних зв'язків",
  "relation.publicationState.draft": "Чернетка",
  "relation.publicationState.published": "Опубліковано",
  "reviewWorkflows.stage.label": "Етап перегляду",
  "select.currently.selected": "{count} зараз вибрано",
  "success.record.clone": "Документ клоновано",
  "success.record.discard": "Зміни відхилено",
  "success.record.delete": "Видалено документ",
  "success.record.publish": "Опубліковано документ",
  "success.record.publishing": "Публікується...",
  "success.record.save": "Збережено",
  "success.record.unpublish": "Скасовано публікацію документа",
  "success.records.delete": "Успішно видалено.",
  "success.records.unpublish": "Успішно скасовано публікацію.",
  "success.records.publish": "Успішно опубліковано.",
  "utils.data-loaded": "Завантажено {number, plural, =1 {# запис} other {# записів}}",
  "listView.validation.errors.title": "Необхідна дія",
  "listView.validation.errors.message": "Будь ласка, переконайтеся, що всі поля правильні перед публікацією (обов’язкові поля, мін/макс обмеження символів тощо).",
  "history.document-action": "Історія вмісту",
  "history.page-title": "Історія {contentType}",
  "history.sidebar.title": "Версії",
  "history.sidebar.version-card.aria-label": "Карточка версії",
  "history.sidebar.versionDescription": "{distanceToNow}{isAnonymous, select, true {} other { автор: {author}}}{isCurrent, select, true { <b>(поточна)</b>} other {}}",
  "history.sidebar.show-newer": "Показати новіші версії",
  "history.sidebar.show-older": "Показати старіші версії",
  "history.version.subtitle": "{hasLocale, select, true {{subtitle}, в {locale}} other {{subtitle}}}",
  "history.content.new-field.title": "Нове поле",
  "history.content.new-field.message": "Це поле не існувало, коли ця версія була збережена. Якщо ви відновите цю версію, воно буде порожнім.",
  "history.content.unknown-fields.title": "Невідомі поля",
  "history.content.unknown-fields.message": "Ці поля були видалені або перейменовані в Конструкторі типів вмісту. <b>Ці поля не будуть відновлені.</b>",
  "history.content.missing-assets.title": "{number, plural, =1 {Відсутній ресурс} other {{number} відсутніх ресурсів}}",
  "history.content.missing-assets.message": "{number, plural, =1 {Його} other {Їх}} було видалено в Медіабібліотеці і не може бути відновлено.",
  "history.content.missing-relations.title": "{number, plural, =1 {Відсутній зв'язок} other {{number} відсутніх зв'язків}}",
  "history.content.missing-relations.message": "{number, plural, =1 {Його} other {Їх}} було видалені і не може бути відновлено.",
  "history.content.no-relations": "Немає зв'язків.",
  "history.content.localized": "Це значення специфічне для цієї локалі. Якщо ви відновите цю версію, вміст не буде замінено для інших локалей.",
  "history.content.not-localized": "Це значення є загальним для всіх локалей. Якщо ви відновите цю версію, вміст буде замінено для всіх локалей.",
  "history.restore.confirm.button": "Відновити",
  "history.restore.confirm.title": "Ви впевнені, що хочете відновити цю версію?",
  "history.restore.confirm.message": "{isDraft, select, true {Відновлений вміст перекриє вашу чернетку.} other {Відновлений вміст не буде опублікований, він перекриє чернетку і буде збережений як очікувані зміни. Ви зможете опублікувати зміни в будь-який час.}}",
  "history.restore.success.title": "Версію відновлено.",
  "history.restore.success.message": "Вміст відновленої версії ще не опубліковано.",
  "history.restore.error.message": "Не вдалося відновити версію.",
  "validation.error": "У вашому документі є помилки. Будь ласка, виправте їх перед збереженням.",
  "bulk-publish.edit": "Редагувати",
  "containers.Edit.Link.Layout": "Налаштувати компонування",
  "containers.Edit.Link.Model": "Змінити Тип Колекцій",
  "containers.Edit.addAnItem": "Додати елемент...",
  "containers.Edit.clickToJump": "Натисніть щоб перейти до запису",
  "containers.Edit.editing": "Редагування...",
  "containers.Edit.pluginHeader.title.new": "Створити запис",
  "containers.Edit.reset": "Скинути",
  "containers.Edit.returnList": "Повернутися до списку",
  "containers.Edit.seeDetails": "Докладніше",
  "containers.Edit.submit": "Зберегти",
  "containers.List.errorFetchRecords": "Помилка",
  "containers.ListSettingsView.modal-form.edit-label": "Налаштуйте підпис",
  "popUpWarning.warning.cancelAllSettings": "Ви впевнені, що хочете скасувати свої зміни?"
};
export {
  uk as default,
  groups,
  models,
  pageNotFound
};
//# sourceMappingURL=uk.json-BTXCC2JR.js.map
