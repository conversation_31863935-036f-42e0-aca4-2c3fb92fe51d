{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/translations/vi.json.mjs"], "sourcesContent": ["var vi = {\n    \"BoundRoute.title\": \"Đến tới\",\n    \"EditForm.inputSelect.description.role\": \"<PERSON><PERSON><PERSON> kèm người dùng mới đã được xác thực vào quyền được chọn.\",\n    \"EditForm.inputSelect.label.role\": \"Quyền mặc định cho các người dùng đã được chứng thực\",\n    \"EditForm.inputToggle.description.email\": \"Không cho phép người dùng tạo nhiều tài khoản có cùng địa chỉ email với nhiều nhà cung cấp chứng thực.\",\n    \"EditForm.inputToggle.description.email-confirmation\": \"<PERSON><PERSON> đư<PERSON> k<PERSON>ch ho<PERSON> (ON), người dùng đăng ký mới nhận được một email xác nhận.\",\n    \"EditForm.inputToggle.description.email-confirmation-redirection\": \"<PERSON>u khi xác nhận email củ<PERSON> bạn, chọn nơi bạn sẽ được đưa về.\",\n    \"EditForm.inputToggle.description.email-reset-password\": \"URL của trang lấy lại mật khẩu của ứng dụng của bạn\",\n    \"EditForm.inputToggle.description.sign-up\": \"Khi không kích hoạt (OFF), quá trình đăng ký bị cấm. Không ai có thể đăng ký thêm dù dùng bất kỳ nhà cung cấp nào.\",\n    \"EditForm.inputToggle.label.email\": \"Một tài khoản trên một địa chỉ email\",\n    \"EditForm.inputToggle.label.email-confirmation\": \"Kích hoạt email xác nhận\",\n    \"EditForm.inputToggle.label.email-confirmation-redirection\": \"URL đưa về\",\n    \"EditForm.inputToggle.label.email-reset-password\": \"Trang lấy lại mật khẩu\",\n    \"EditForm.inputToggle.label.sign-up\": \"Kích hoạt đăng ký\",\n    \"HeaderNav.link.advancedSettings\": \"Cài đặt nâng cao\",\n    \"HeaderNav.link.emailTemplates\": \"Mẫu email\",\n    \"HeaderNav.link.providers\": \"Các nhà cung cấp\",\n    \"Plugin.permissions.plugins.description\": \"Định nghĩa tất cả hành động được phép cho {name} plugin.\",\n    \"Plugins.header.description\": \"Chỉ các hành động đến bởi một đường dẫn được liệt kê bên dưới.\",\n    \"Plugins.header.title\": \"Các Quyền\",\n    \"Policies.header.hint\": \"Chọn các hành động của ứng dựng hoặc hành động của plugin và nhấn vào biểu tượng bánh răng để hiển thị đường đến\",\n    \"Policies.header.title\": \"Các cài đặt nâng cao\",\n    \"PopUpForm.Email.email_templates.inputDescription\": \"Nếu bạn không chắc sử dụng các biến như thế nào, {link}\",\n    \"PopUpForm.Email.options.from.email.label\": \"Email người gửi\",\n    \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Email.options.from.name.label\": \"Tên người gửi\",\n    \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n    \"PopUpForm.Email.options.message.label\": \"Thông điệp\",\n    \"PopUpForm.Email.options.object.label\": \"Chủ đề\",\n    \"PopUpForm.Email.options.response_email.label\": \"Email phản hồi\",\n    \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Providers.enabled.description\": \"Nếu không kích hoạt, người dùng sẽ không thể dùng nhà cung cấp này.\",\n    \"PopUpForm.Providers.enabled.label\": \"Kích hoạt\",\n    \"PopUpForm.Providers.key.label\": \"Client ID\",\n    \"PopUpForm.Providers.key.placeholder\": \"VĂN BẢN\",\n    \"PopUpForm.Providers.redirectURL.front-end.label\": \"URL chuyển tiếp đến ứng dụng bên ngoài của bạn\",\n    \"PopUpForm.Providers.secret.label\": \"Client Secret\",\n    \"PopUpForm.Providers.secret.placeholder\": \"VĂN BẢN\",\n    \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n    \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n    \"PopUpForm.header.edit.email-templates\": \"Sửa Các Mẫu Email\",\n    \"notification.success.submit\": \"Các cấu hình đã được cập nhật\",\n    \"plugin.description.long\": \"Bảo vệ API của bạn với quá trình chứng thực đầy đủ dựa trên JWT. Plugin này cũng kèm với chiến lược ACL cho phép bạn quản lý quyền giữa các nhóm người dùng.\",\n    \"plugin.description.short\": \"Bảo vệ API của bạn với quá trình chứng thực đầy đủ dựa trên JWT\",\n    \"plugin.name\": \"Vai trò và Quyền\"\n};\n\nexport { vi as default };\n//# sourceMappingURL=vi.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AACnB;", "names": []}