"use strict";
/**
 * Custom property routes - consolidated
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        // User properties management
        {
            method: 'GET',
            path: '/properties/my-properties',
            handler: 'property.getMyProperties',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'GET',
            path: '/properties/featured',
            handler: 'property.getFeatured',
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'GET',
            path: '/properties/:id/edit',
            handler: 'property.getForEdit',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        // Publishing controls
        {
            method: 'PUT',
            path: '/properties/:id/publish',
            handler: 'property.publish',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'PUT',
            path: '/properties/:id/unpublish',
            handler: 'property.unpublish',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        // Nearby places functionality
        {
            method: 'POST',
            path: '/properties/nearby-places',
            handler: 'property.findNearbyPlaces',
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/properties/:id/generate-nearby-places',
            handler: 'property.generateNearbyPlaces',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'GET',
            path: '/properties/:id/nearby-places',
            handler: 'property.getNearbyPlaces',
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },
    ],
};
