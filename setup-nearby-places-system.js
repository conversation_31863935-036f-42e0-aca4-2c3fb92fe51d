// Complete setup script for nearby places system
const axios = require('axios');
const { GOOGLE_PLACE_TYPES } = require('./backend/src/api/nearby-place-category/services/google-place-types');

const API_BASE = 'http://localhost:1337/api';
const ADMIN_BASE = 'http://localhost:1337/admin';

// Test user credentials
const userCredentials = {
  email: '<EMAIL>',
  password: 'Mb123321'
};

const defaultCategories = [
  {
    name: 'education',
    displayName: 'Education',
    description: 'Schools, universities, libraries, and educational institutions',
    icon: '🎓',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.SCHOOL,
      GOOGLE_PLACE_TYPES.PRIMARY_SCHOOL,
      GOOGLE_PLACE_TYPES.SECONDARY_SCHOOL,
      GOOGLE_PLACE_TYPES.UNIVERSITY,
      GOOGLE_PLACE_TYPES.LIBRARY
    ],
    enabled: true,
    searchRadius: 2000,
    maxResults: 10,
    priority: 10,
    color: '#10B981'
  },
  {
    name: 'restaurants',
    displayName: 'Restaurants & Food',
    description: 'Restaurants, cafes, bars, and food establishments',
    icon: '🍽️',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.RESTAURANT,
      GOOGLE_PLACE_TYPES.CAFE,
      GOOGLE_PLACE_TYPES.BAR,
      GOOGLE_PLACE_TYPES.BAKERY,
      GOOGLE_PLACE_TYPES.MEAL_DELIVERY,
      GOOGLE_PLACE_TYPES.MEAL_TAKEAWAY
    ],
    enabled: true,
    searchRadius: 1000,
    maxResults: 15,
    priority: 9,
    color: '#F59E0B'
  },
  {
    name: 'shopping',
    displayName: 'Shopping',
    description: 'Shopping malls, stores, supermarkets, and retail',
    icon: '🛍️',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.SHOPPING_MALL,
      GOOGLE_PLACE_TYPES.SUPERMARKET,
      GOOGLE_PLACE_TYPES.CONVENIENCE_STORE,
      GOOGLE_PLACE_TYPES.DEPARTMENT_STORE,
      GOOGLE_PLACE_TYPES.CLOTHING_STORE,
      GOOGLE_PLACE_TYPES.ELECTRONICS_STORE,
      GOOGLE_PLACE_TYPES.STORE
    ],
    enabled: true,
    searchRadius: 1500,
    maxResults: 10,
    priority: 8,
    color: '#8B5CF6'
  },
  {
    name: 'healthcare',
    displayName: 'Healthcare',
    description: 'Hospitals, clinics, pharmacies, and medical facilities',
    icon: '🏥',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.HOSPITAL,
      GOOGLE_PLACE_TYPES.DOCTOR,
      GOOGLE_PLACE_TYPES.DENTIST,
      GOOGLE_PLACE_TYPES.PHARMACY,
      GOOGLE_PLACE_TYPES.PHYSIOTHERAPIST,
      GOOGLE_PLACE_TYPES.VETERINARY_CARE
    ],
    enabled: true,
    searchRadius: 2000,
    maxResults: 8,
    priority: 9,
    color: '#EF4444'
  },
  {
    name: 'transportation',
    displayName: 'Transportation',
    description: 'Bus stops, train stations, airports, and transit hubs',
    icon: '🚌',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.BUS_STATION,
      GOOGLE_PLACE_TYPES.TRAIN_STATION,
      GOOGLE_PLACE_TYPES.SUBWAY_STATION,
      GOOGLE_PLACE_TYPES.LIGHT_RAIL_STATION,
      GOOGLE_PLACE_TYPES.TRANSIT_STATION,
      GOOGLE_PLACE_TYPES.TAXI_STAND,
      GOOGLE_PLACE_TYPES.AIRPORT,
      GOOGLE_PLACE_TYPES.GAS_STATION
    ],
    enabled: true,
    searchRadius: 2000,
    maxResults: 8,
    priority: 7,
    color: '#3B82F6'
  }
];

async function checkAPIEndpoint() {
  try {
    console.log('🔍 Checking API endpoint...');
    const response = await axios.get(`${API_BASE}/nearby-place-categories`);
    console.log('✅ API endpoint is accessible');
    return true;
  } catch (error) {
    console.log(`❌ API endpoint error: ${error.response?.status} - ${error.response?.statusText}`);
    if (error.response?.status === 403) {
      console.log('🔒 Permission denied - need to configure permissions in Strapi admin');
    } else if (error.response?.status === 404) {
      console.log('🚫 Endpoint not found - content type may not be registered');
    }
    return false;
  }
}

async function testUserLogin() {
  try {
    console.log('🔐 Testing user login...');
    const loginResponse = await axios.post(`${API_BASE}/auth/local`, {
      identifier: userCredentials.email,
      password: userCredentials.password
    });
    console.log('✅ User login successful');
    return loginResponse.data.jwt;
  } catch (error) {
    console.log('❌ User login failed:', error.response?.data?.error?.message || error.message);
    return null;
  }
}

async function createCategoriesWithToken(token) {
  console.log('\n📋 Creating place categories...');
  let successCount = 0;
  let existingCount = 0;
  let errorCount = 0;

  for (const category of defaultCategories) {
    try {
      const response = await axios.post(`${API_BASE}/nearby-place-categories`, {
        data: category
      }, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log(`✅ Created: ${category.displayName}`);
      successCount++;
    } catch (error) {
      if (error.response?.status === 400 && error.response?.data?.error?.message?.includes('unique')) {
        console.log(`⚠️  Already exists: ${category.displayName}`);
        existingCount++;
      } else {
        console.error(`❌ Failed: ${category.displayName} - ${error.response?.data?.error?.message || error.message}`);
        errorCount++;
      }
    }
  }

  return { successCount, existingCount, errorCount };
}

async function setupNearbyPlacesSystem() {
  console.log('🚀 Setting up Nearby Places System...\n');

  // Step 1: Check API endpoint
  const apiAccessible = await checkAPIEndpoint();
  
  // Step 2: Test user login
  const userToken = await testUserLogin();
  
  if (!userToken) {
    console.log('\n❌ Cannot proceed without valid authentication');
    return;
  }

  // Step 3: Try to create categories
  if (apiAccessible) {
    const results = await createCategoriesWithToken(userToken);
    
    console.log('\n📊 Results Summary:');
    console.log(`✅ Successfully created: ${results.successCount}`);
    console.log(`⚠️  Already existed: ${results.existingCount}`);
    console.log(`❌ Failed: ${results.errorCount}`);
    
    if (results.successCount > 0 || results.existingCount > 0) {
      console.log('\n🎉 Nearby Places System is ready!');
      console.log('\n💡 Next steps:');
      console.log('1. Test property submission with coordinates');
      console.log('2. Check nearby places detection on property pages');
      console.log('3. Adjust category settings in Strapi admin if needed');
    }
  } else {
    console.log('\n🔧 Manual Setup Required:');
    console.log('1. Go to http://localhost:1337/admin');
    console.log('2. Navigate to Settings > Users & Permissions Plugin > Roles');
    console.log('3. Edit "Authenticated" role');
    console.log('4. Find "Nearby-place-category" and enable:');
    console.log('   - find');
    console.log('   - findOne');
    console.log('   - create (if users should create categories)');
    console.log('5. Save permissions');
    console.log('6. Run this script again');
    
    console.log('\n📝 Or manually create categories in admin panel:');
    defaultCategories.forEach((cat, i) => {
      console.log(`${i + 1}. ${cat.displayName} (${cat.name})`);
    });
  }
}

// Run the setup
setupNearbyPlacesSystem();
