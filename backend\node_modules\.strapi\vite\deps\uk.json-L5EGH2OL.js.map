{"version": 3, "sources": ["../../../@strapi/email/dist/admin/translations/uk.json.mjs"], "sourcesContent": ["var link = \"Посилання\";\nvar uk = {\n    link: link,\n    \"Settings.email.plugin.button.test-email\": \"Надіслати тестовий лист\",\n    \"Settings.email.plugin.label.defaultFrom\": \"Електронна пошта відправника за замовчуванням\",\n    \"Settings.email.plugin.label.defaultReplyTo\": \"Електронна пошта для відповіді за замовчуванням\",\n    \"Settings.email.plugin.label.provider\": \"Постачальник електронної пошти\",\n    \"Settings.email.plugin.label.testAddress\": \"Електронна адреса отримувача\",\n    \"Settings.email.plugin.notification.config.error\": \"Не вдалося отримати конфігурацію електронної пошти\",\n    \"Settings.email.plugin.notification.data.loaded\": \"Дані налаштувань електронної пошти завантажено\",\n    \"Settings.email.plugin.notification.test.error\": \"Не вдалося відправити тестовий лист на {to}\",\n    \"Settings.email.plugin.notification.test.success\": \"Тест електронної пошти успішно виконано, перевірте поштову скриньку {to}\",\n    \"Settings.email.plugin.placeholder.defaultFrom\": \"нп: Strapi No-Reply <<EMAIL>>\",\n    \"Settings.email.plugin.placeholder.defaultReplyTo\": \"нп: Strapi <<EMAIL>>\",\n    \"Settings.email.plugin.placeholder.testAddress\": \"нп: <EMAIL>\",\n    \"Settings.email.plugin.subTitle\": \"Перевірте налаштування плаґіна електронної пошти\",\n    \"Settings.email.plugin.text.configuration\": \"Плаґін налаштовано через файл {file}, перегляньте це посилання {link} для документації.\",\n    \"Settings.email.plugin.title\": \"Конфігурація\",\n    \"Settings.email.plugin.title.config\": \"Конфігурація\",\n    \"Settings.email.plugin.title.test\": \"Тест доставки електронної пошти\",\n    \"SettingsNav.link.settings\": \"Налаштування\",\n    \"SettingsNav.section-label\": \"Плаґін електронної пошти\",\n    \"components.Input.error.validation.email\": \"Це неправильна електронна адреса\"\n};\n\nexport { uk as default, link };\n//# sourceMappingURL=uk.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL;AAAA,EACA,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,iDAAiD;AAAA,EACjD,oDAAoD;AAAA,EACpD,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,2CAA2C;AAC/C;", "names": []}