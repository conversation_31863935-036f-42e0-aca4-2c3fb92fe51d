// Script to check available memberships
const axios = require('axios');

const API_BASE = 'http://localhost:1337/api';

async function checkMemberships() {
  try {
    console.log('📋 Fetching available memberships...');
    
    const membershipsResponse = await axios.get(`${API_BASE}/memberships`);
    const memberships = membershipsResponse.data.data;
    
    console.log(`Found ${memberships.length} memberships:`);
    
    memberships.forEach((membership, index) => {
      console.log(`\n${index + 1}. ${membership.name} (ID: ${membership.id})`);
      console.log(`   Slug: ${membership.slug}`);
      console.log(`   Max Properties: ${membership.maxProperties}`);
      console.log(`   Max Images: ${membership.maxImages}`);
      console.log(`   Price: ${membership.price} ${membership.currency}`);
      console.log(`   Active: ${membership.isActive}`);
    });
    
  } catch (error) {
    console.error('❌ Failed to fetch memberships:', error.response?.data || error.message);
  }
}

// Run the check
checkMemberships();
