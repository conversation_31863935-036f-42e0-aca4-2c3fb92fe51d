'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { MapPin, Navigation, Search } from 'lucide-react';

interface Coordinates {
  lat: number;
  lng: number;
}

interface CoordinateSelectorProps {
  value?: Coordinates;
  onChange: (coordinates: Coordinates) => void;
  address?: string;
  className?: string;
}

export const CoordinateSelector: React.FC<CoordinateSelectorProps> = ({
  value,
  onChange,
  address,
  className = ''
}) => {
  const [coordinates, setCoordinates] = useState<Coordinates | null>(value || null);
  const [manualInput, setManualInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Auto-detect coordinates from address
  const geocodeAddress = useCallback(async (addressToGeocode: string) => {
    if (!addressToGeocode.trim()) return;

    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    if (!apiKey) {
      setError('Google Maps API key not configured');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(addressToGeocode)}&key=${apiKey}`
      );
      const data = await response.json();

      if (data.status === 'OK' && data.results.length > 0) {
        const location = data.results[0].geometry.location;
        const newCoordinates = { lat: location.lat, lng: location.lng };
        setCoordinates(newCoordinates);
        onChange(newCoordinates);
      } else {
        setError('Address not found');
      }
    } catch (err) {
      setError('Failed to geocode address');
    } finally {
      setIsLoading(false);
    }
  }, [onChange]);

  // Auto-detect from address prop
  useEffect(() => {
    if (address && !coordinates) {
      geocodeAddress(address);
    }
  }, [address, coordinates, geocodeAddress]);

  // Get current location
  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      setError('Geolocation is not supported by this browser');
      return;
    }

    setIsLoading(true);
    setError(null);

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const newCoordinates = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };
        setCoordinates(newCoordinates);
        onChange(newCoordinates);
        setIsLoading(false);
      },
      (error) => {
        setError('Failed to get current location');
        setIsLoading(false);
      }
    );
  };

  // Handle manual coordinate input
  const handleManualInput = () => {
    const parts = manualInput.split(',').map(s => s.trim());
    if (parts.length === 2) {
      const lat = parseFloat(parts[0]);
      const lng = parseFloat(parts[1]);
      
      if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
        const newCoordinates = { lat, lng };
        setCoordinates(newCoordinates);
        onChange(newCoordinates);
        setError(null);
      } else {
        setError('Invalid coordinates. Please enter valid latitude and longitude.');
      }
    } else {
      setError('Please enter coordinates in format: latitude, longitude');
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center space-x-2 text-sm font-medium text-gray-700">
        <MapPin className="h-4 w-4" />
        <span>Property Location</span>
      </div>

      {/* Current Coordinates Display */}
      {coordinates && (
        <div className="p-3 bg-green-50 border border-green-200 rounded-md">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-green-800">Location Set</p>
              <p className="text-xs text-green-600">
                {coordinates.lat.toFixed(6)}, {coordinates.lng.toFixed(6)}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <button
                type="button"
                onClick={() => {
                  setCoordinates(null);
                  setManualInput('');
                }}
                className="text-xs text-green-600 hover:text-green-800"
              >
                Clear
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Auto-detect from Address */}
      {address && !coordinates && (
        <button
          type="button"
          onClick={() => geocodeAddress(address)}
          disabled={isLoading}
          className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-50 text-blue-700 border border-blue-200 rounded-md hover:bg-blue-100 disabled:opacity-50"
        >
          <Search className="h-4 w-4" />
          <span>{isLoading ? 'Detecting...' : 'Auto-detect from Address'}</span>
        </button>
      )}

      {/* Current Location Button */}
      <button
        type="button"
        onClick={getCurrentLocation}
        disabled={isLoading}
        className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-green-50 text-green-700 border border-green-200 rounded-md hover:bg-green-100 disabled:opacity-50"
      >
        <Navigation className="h-4 w-4" />
        <span>{isLoading ? 'Getting Location...' : 'Use Current Location'}</span>
      </button>

      {/* Manual Input */}
      <div className="space-y-2">
        <label className="block text-sm font-medium text-gray-700">
          Or Enter Coordinates Manually
        </label>
        <div className="flex space-x-2">
          <input
            type="text"
            value={manualInput}
            onChange={(e) => setManualInput(e.target.value)}
            placeholder="latitude, longitude (e.g., 40.7128, -74.0060)"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
          />
          <button
            type="button"
            onClick={handleManualInput}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
          >
            Set
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-md">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Help Text */}
      <div className="text-xs text-gray-500">
        <p>Setting precise coordinates helps find the most relevant nearby places.</p>
        <p>You can use the property address, current location, or enter coordinates manually.</p>
      </div>
    </div>
  );
};
