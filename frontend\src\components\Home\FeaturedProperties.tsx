'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { MapPin, Bed, Bath, Square, Eye, Star, Crown } from 'lucide-react';
import { propertiesAPI } from '@/lib/api';

interface Property {
  id: number;
  documentId: string;
  title: string;
  price: number;
  currency: string;
  city: string;
  address: string;
  neighborhood?: string;
  bedrooms?: number;
  bathrooms?: number;
  area: number;
  areaUnit: string;
  images?: any[];
  views: number;
  featured: boolean;
  isLuxury?: boolean;
  propertyType: string;
  status: string;
}

const FeaturedProperties: React.FC = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeaturedProperties = async () => {
      try {
        setLoading(true);
        console.log('Fetching featured properties...');
        const response = await propertiesAPI.getFeatured();
        console.log('Featured properties response:', response);

        if (response.data && response.data.length > 0) {
          setProperties(response.data);
          setError(null);
        } else {
          console.log('No featured properties found, trying to get recent properties...');
          // Try to get recent properties if no featured ones exist
          try {
            const recentResponse = await propertiesAPI.getAll({
              sort: ['createdAt:desc'],
              pagination: { limit: 6 }
            });
            if (recentResponse.data && recentResponse.data.length > 0) {
              setProperties(recentResponse.data);
              setError(null);
            } else {
              throw new Error('No properties found');
            }
          } catch (recentErr) {
            console.log('No recent properties found either, using mock data');
            setError('No properties available');
            // Fallback to mock data
            setProperties([
          {
            id: 1,
            documentId: 'mock-1',
            title: 'Modern Luxury Villa',
            price: 850000,
            currency: 'USD',
            city: 'Beverly Hills',
            address: 'Beverly Hills, CA',
            bedrooms: 4,
            bathrooms: 3,
            area: 232,
            areaUnit: 'sqm',
            views: 245,
            featured: true,
            isLuxury: true,
            propertyType: 'villa',
            status: 'for-sale'
          },
          {
            id: 2,
            documentId: 'mock-2',
            title: 'Downtown Penthouse',
            price: 1200000,
            currency: 'USD',
            city: 'Manhattan',
            address: 'Manhattan, NY',
            bedrooms: 3,
            bathrooms: 2,
            area: 167,
            areaUnit: 'sqm',
            views: 189,
            featured: true,
            isLuxury: true,
            propertyType: 'penthouse',
            status: 'for-sale'
          },
          {
            id: 3,
            documentId: 'mock-3',
            title: 'Seaside Apartment',
            price: 650000,
            currency: 'USD',
            city: 'Miami Beach',
            address: 'Miami Beach, FL',
            bedrooms: 2,
            bathrooms: 2,
            area: 111,
            areaUnit: 'sqm',
            views: 156,
            featured: true,
            propertyType: 'apartment',
            status: 'for-sale'
          }
        ]);
          }
        }
      } catch (err) {
        console.error('Error fetching featured properties:', err);
        setError('Failed to load featured properties');
        // Fallback to mock data when API fails
        setProperties([
          {
            id: 1,
            documentId: 'mock-1',
            title: 'Modern Luxury Villa',
            price: 850000,
            currency: 'USD',
            city: 'Beverly Hills',
            address: 'Beverly Hills, CA',
            bedrooms: 4,
            bathrooms: 3,
            area: 232,
            areaUnit: 'sqm',
            views: 245,
            featured: true,
            isLuxury: true,
            propertyType: 'villa',
            status: 'for-sale'
          },
          {
            id: 2,
            documentId: 'mock-2',
            title: 'Downtown Penthouse',
            price: 1200000,
            currency: 'USD',
            city: 'Manhattan',
            address: 'Manhattan, NY',
            bedrooms: 3,
            bathrooms: 2,
            area: 167,
            areaUnit: 'sqm',
            views: 189,
            featured: true,
            isLuxury: true,
            propertyType: 'penthouse',
            status: 'for-sale'
          },
          {
            id: 3,
            documentId: 'mock-3',
            title: 'Seaside Apartment',
            price: 650000,
            currency: 'USD',
            city: 'Miami Beach',
            address: 'Miami Beach, FL',
            bedrooms: 2,
            bathrooms: 2,
            area: 111,
            areaUnit: 'sqm',
            views: 156,
            featured: true,
            propertyType: 'apartment',
            status: 'for-sale'
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedProperties();
  }, []);

  const getImageUrl = (property: Property) => {
    if (property.images && property.images.length > 0) {
      const image = property.images[0];
      return `${process.env.NEXT_PUBLIC_STRAPI_URL}${image.url}`;
    }
    return '/api/placeholder/400/300';
  };

  const formatPrice = (price: number, currency: string) => {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });
    return formatter.format(price);
  };

  if (loading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Featured Properties
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Discover our handpicked selection of premium properties
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-white rounded-lg shadow-lg overflow-hidden animate-pulse">
                <div className="h-48 bg-gray-300"></div>
                <div className="p-6">
                  <div className="h-6 bg-gray-300 rounded mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded mb-3"></div>
                  <div className="h-4 bg-gray-300 rounded mb-4"></div>
                  <div className="h-8 bg-gray-300 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Featured Properties
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Discover our handpicked selection of premium properties
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {properties.map((property) => (
            <div key={property.documentId} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className="relative">
                <img
                  src={getImageUrl(property)}
                  alt={property.title}
                  className="w-full h-48 object-cover"
                />
                <div className="absolute top-4 left-4 flex gap-2">
                  <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center">
                    <Star className="h-3 w-3 mr-1" />
                    Featured
                  </span>
                  {property.isLuxury && (
                    <span className="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center">
                      <Crown className="h-3 w-3 mr-1" />
                      Luxury
                    </span>
                  )}
                </div>
                <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-2 py-1 rounded flex items-center">
                  <Eye className="h-4 w-4 mr-1" />
                  <span className="text-sm">{property.views}</span>
                </div>
                <div className="absolute bottom-4 left-4">
                  <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                    property.status === 'for-sale'
                      ? 'bg-green-500 text-white'
                      : property.status === 'for-rent'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-500 text-white'
                  }`}>
                    {property.status === 'for-sale' ? 'For Sale' :
                     property.status === 'for-rent' ? 'For Rent' :
                     property.status}
                  </span>
                </div>
              </div>

              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-xl font-semibold text-gray-900">
                    {property.title}
                  </h3>
                  <span className="text-sm text-gray-500 capitalize bg-gray-100 px-2 py-1 rounded">
                    {property.propertyType}
                  </span>
                </div>

                <div className="flex items-center text-gray-600 mb-3">
                  <MapPin className="h-4 w-4 mr-1" />
                  <span className="text-sm">
                    {property.neighborhood ? `${property.neighborhood}, ` : ''}{property.city}
                  </span>
                </div>

                {(property.bedrooms || property.bathrooms || property.area) && (
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      {property.bedrooms && (
                        <div className="flex items-center">
                          <Bed className="h-4 w-4 mr-1" />
                          <span>{property.bedrooms}</span>
                        </div>
                      )}
                      {property.bathrooms && (
                        <div className="flex items-center">
                          <Bath className="h-4 w-4 mr-1" />
                          <span>{property.bathrooms}</span>
                        </div>
                      )}
                      {property.area && (
                        <div className="flex items-center">
                          <Square className="h-4 w-4 mr-1" />
                          <span>{property.area} {property.areaUnit || 'sqm'}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <div className="text-2xl font-bold text-blue-600">
                    {formatPrice(property.price, property.currency)}
                  </div>
                  <Link
                    href={`/properties/${property.documentId}`}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-semibold"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link
            href="/properties"
            className="bg-blue-600 text-white px-8 py-3 rounded-md font-semibold hover:bg-blue-700 transition-colors"
          >
            View All Properties
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProperties;
