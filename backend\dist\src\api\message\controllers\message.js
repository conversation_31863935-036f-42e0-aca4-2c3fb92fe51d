"use strict";
/**
 * message controller
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreController('api::message.message', ({ strapi }) => ({
    // Custom find method - only show user's messages
    async find(ctx) {
        const user = ctx.state.user;
        if (!user) {
            return ctx.unauthorized('You must be logged in to view messages');
        }
        const messages = await strapi.entityService.findMany('api::message.message', {
            filters: {
                $or: [
                    { sender: user.id },
                    { recipient: user.id }
                ]
            },
            populate: ['sender', 'recipient', 'property', 'project', 'attachments', 'parentMessage'],
            sort: { createdAt: 'desc' }
        });
        return { data: messages };
    },
    // Custom findOne method
    async findOne(ctx) {
        var _a, _b;
        const { id } = ctx.params;
        const user = ctx.state.user;
        if (!user) {
            return ctx.unauthorized('You must be logged in to view messages');
        }
        const message = await strapi.entityService.findOne('api::message.message', id, {
            populate: ['sender', 'recipient', 'property', 'project', 'attachments', 'parentMessage', 'replies']
        });
        if (!message) {
            return ctx.notFound('Message not found');
        }
        // Check if user is sender or recipient
        // @ts-ignore
        const senderId = ((_a = message.sender) === null || _a === void 0 ? void 0 : _a.id) || message.sender;
        // @ts-ignore
        const recipientId = ((_b = message.recipient) === null || _b === void 0 ? void 0 : _b.id) || message.recipient;
        if (senderId !== user.id && recipientId !== user.id) {
            return ctx.forbidden('You can only view your own messages');
        }
        // Mark as read if user is recipient
        if (recipientId === user.id && !message.isRead) {
            await strapi.entityService.update('api::message.message', id, {
                data: {
                    isRead: true,
                    readAt: new Date()
                }
            });
        }
        return { data: message };
    },
    // Custom create method
    async create(ctx) {
        const user = ctx.state.user;
        if (!user) {
            return ctx.unauthorized('You must be logged in to send messages');
        }
        ctx.request.body.data.sender = user.id;
        const response = await super.create(ctx);
        return response;
    },
    // Get inbox messages
    async inbox(ctx) {
        const user = ctx.state.user;
        if (!user) {
            return ctx.unauthorized('You must be logged in');
        }
        const messages = await strapi.entityService.findMany('api::message.message', {
            filters: { recipient: user.id },
            populate: ['sender', 'property', 'project'],
            sort: { createdAt: 'desc' }
        });
        return { data: messages };
    },
    // Get sent messages
    async sent(ctx) {
        const user = ctx.state.user;
        if (!user) {
            return ctx.unauthorized('You must be logged in');
        }
        const messages = await strapi.entityService.findMany('api::message.message', {
            filters: { sender: user.id },
            populate: ['recipient', 'property', 'project'],
            sort: { createdAt: 'desc' }
        });
        return { data: messages };
    },
    // Mark message as read
    async markAsRead(ctx) {
        var _a;
        const { id } = ctx.params;
        const user = ctx.state.user;
        if (!user) {
            return ctx.unauthorized('You must be logged in');
        }
        const message = await strapi.entityService.findOne('api::message.message', id, {
            populate: ['recipient']
        });
        if (!message) {
            return ctx.notFound('Message not found');
        }
        // @ts-ignore
        const recipientId = ((_a = message.recipient) === null || _a === void 0 ? void 0 : _a.id) || message.recipient;
        if (recipientId !== user.id) {
            return ctx.forbidden('You can only mark your own messages as read');
        }
        const updatedMessage = await strapi.entityService.update('api::message.message', id, {
            data: {
                isRead: true,
                readAt: new Date()
            }
        });
        return { data: updatedMessage };
    }
}));
