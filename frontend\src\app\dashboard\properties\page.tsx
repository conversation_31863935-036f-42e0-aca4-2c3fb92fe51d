'use client';

import React, { useState, useEffect } from 'react';
import DashboardLayout from '@/components/Dashboard/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import { propertiesAPI } from '@/lib/api';
import {
  Building2,
  Search,
  Filter,
  Edit,
  Eye,
  Trash2,
  Plus,
  MapPin,
  DollarSign,
  Calendar,
  Home,
  Bed,
  Bath,
  Square,
  MoreVertical,
  Grid3X3,
  List,
  Globe,
  FileText,
  Settings
} from 'lucide-react';
import Link from 'next/link';

interface Property {
  id: string;
  title: string;
  description: string;
  price: number;
  currency: string;
  propertyType: string;
  offer: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  address: string;
  city: string;
  state: string;
  country: string;
  neighborhood: string[] | Array<{
    name: string;
    type: string;
    formatted_address: string;
    place_id?: string;
  }> | string | null;
  publishedAt: string | null;
  createdAt: string;
  updatedAt: string;
  images?: any[];
}

// Helper function to normalize neighborhood data
const normalizeNeighborhoods = (neighborhood: any): Array<{ name: string; type: string; formatted_address: string; place_id?: string }> => {
  if (!neighborhood) return [];

  if (typeof neighborhood === 'string') {
    // Convert old string format to new format
    return [{
      name: neighborhood,
      type: 'neighborhood',
      formatted_address: neighborhood
    }];
  }

  if (Array.isArray(neighborhood)) {
    // Check if it's array of strings (new simple format) or array of objects (old complex format)
    if (neighborhood.length > 0 && typeof neighborhood[0] === 'string') {
      // Convert array of strings to object format for display
      return neighborhood.map((name: string) => ({
        name: name,
        type: 'neighborhood',
        formatted_address: name
      }));
    }
    // Already in object format
    return neighborhood;
  }

  return [];
};

const MyPropertiesPage: React.FC = () => {
  const { user } = useAuth();
  const [properties, setProperties] = useState<Property[]>([]);
  const [filteredProperties, setFilteredProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [publishFilter, setPublishFilter] = useState('all');
  const [sortBy, setSortBy] = useState('newest');
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  useEffect(() => {
    const fetchProperties = async () => {
      try {
        const response = await propertiesAPI.getMyProperties();
        const propertiesData = Array.isArray(response) ? response : response.data || [];
        setProperties(propertiesData);
        setFilteredProperties(propertiesData);
      } catch (error) {
        console.error('Failed to fetch properties:', error);
        setProperties([]);
        setFilteredProperties([]);
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchProperties();
    } else {
      setLoading(false);
    }
  }, [user]);

  // Apply filters and search
  useEffect(() => {
    let filtered = [...properties];

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(property =>
        property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        property.address.toLowerCase().includes(searchTerm.toLowerCase()) ||
        property.city.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(property => property.status === statusFilter);
    }

    // Type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(property => property.propertyType === typeFilter);
    }

    // Publish filter
    if (publishFilter !== 'all') {
      if (publishFilter === 'published') {
        filtered = filtered.filter(property => property.publishedAt);
      } else if (publishFilter === 'draft') {
        filtered = filtered.filter(property => !property.publishedAt);
      }
    }

    // Sort
    switch (sortBy) {
      case 'newest':
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'title':
        filtered.sort((a, b) => a.title.localeCompare(b.title));
        break;
    }

    setFilteredProperties(filtered);
  }, [properties, searchTerm, statusFilter, typeFilter, publishFilter, sortBy]);

  const handleDelete = async (id: string) => {
    if (confirm('Are you sure you want to delete this property?')) {
      try {
        await propertiesAPI.delete(id);
        setProperties(properties.filter(p => p.id !== id));
      } catch (error) {
        console.error('Failed to delete property:', error);
        alert('Failed to delete property. Please try again.');
      }
    }
  };

  const handleTogglePublish = async (property: Property) => {
    try {
      if (property.publishedAt) {
        await propertiesAPI.unpublish(property.id);
      } else {
        await propertiesAPI.publish(property.id);
      }
      
      // Refresh properties
      const response = await propertiesAPI.getMyProperties();
      const propertiesData = Array.isArray(response) ? response : response.data || [];
      setProperties(propertiesData);
    } catch (error) {
      console.error('Failed to toggle publish status:', error);
      alert('Failed to update property status. Please try again.');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'for_sale':
        return 'bg-green-100 text-green-800';
      case 'for_rent':
        return 'bg-blue-100 text-blue-800';
      case 'sold':
        return 'bg-gray-100 text-gray-800';
      case 'rented':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'house':
        return Home;
      case 'apartment':
        return Building2;
      default:
        return Building2;
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => setActiveDropdown(null);
    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const PropertyDropdownMenu = ({ property }: { property: Property }) => (
    <div className="relative">
      <button
        onClick={(e) => {
          e.stopPropagation();
          setActiveDropdown(activeDropdown === property.id ? null : property.id);
        }}
        className="p-1 hover:bg-gray-100 rounded transition-colors"
      >
        <MoreVertical className="h-4 w-4 text-gray-400" />
      </button>

      {activeDropdown === property.id && (
        <div className="absolute right-0 top-8 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10">
          <Link
            href={`/properties/${property.id}`}
            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
          >
            <Eye className="h-4 w-4 mr-3" />
            View Property
          </Link>
          <Link
            href={`/dashboard/properties/${property.id}/edit`}
            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
          >
            <Edit className="h-4 w-4 mr-3" />
            Edit Property
          </Link>
          <button
            onClick={() => {
              handleTogglePublish(property);
              setActiveDropdown(null);
            }}
            className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
          >
            {property.publishedAt ? (
              <>
                <FileText className="h-4 w-4 mr-3" />
                Unpublish
              </>
            ) : (
              <>
                <Globe className="h-4 w-4 mr-3" />
                Publish
              </>
            )}
          </button>
          <div className="border-t border-gray-100 my-1"></div>
          <button
            onClick={() => {
              handleDelete(property.id);
              setActiveDropdown(null);
            }}
            className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4 mr-3" />
            Delete Property
          </button>
        </div>
      )}
    </div>
  );

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Properties</h1>
            <p className="text-gray-600 mt-1">
              Manage your property listings ({filteredProperties.length} of {properties.length})
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {/* View Mode Toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="Grid View"
              >
                <Grid3X3 className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'list'
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
                title="List View"
              >
                <List className="h-4 w-4" />
              </button>
            </div>

            <Link
              href="/submit-property"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Property
            </Link>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search properties..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </button>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Status</option>
                    <option value="for_sale">For Sale</option>
                    <option value="for_rent">For Rent</option>
                    <option value="sold">Sold</option>
                    <option value="rented">Rented</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                  <select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Types</option>
                    <option value="house">House</option>
                    <option value="apartment">Apartment</option>
                    <option value="condo">Condo</option>
                    <option value="townhouse">Townhouse</option>
                    <option value="villa">Villa</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Publication</label>
                  <select
                    value={publishFilter}
                    onChange={(e) => setPublishFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All</option>
                    <option value="published">Published</option>
                    <option value="draft">Draft</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="newest">Newest First</option>
                    <option value="oldest">Oldest First</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="title">Title A-Z</option>
                  </select>
                </div>

                <div className="flex items-end">
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      setStatusFilter('all');
                      setTypeFilter('all');
                      setPublishFilter('all');
                      setSortBy('newest');
                    }}
                    className="w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    Clear Filters
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Properties Display */}
        {filteredProperties.length > 0 ? (
          viewMode === 'grid' ? (
            /* Grid View */
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {filteredProperties.map((property) => {
                const TypeIcon = getTypeIcon(property.propertyType);
                return (
                  <div key={property.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
                    {/* Property Image */}
                    <div className="h-48 bg-gray-200 relative">
                      {property.images && property.images.length > 0 ? (
                        <img
                          src={property.images[0].url}
                          alt={property.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Building2 className="h-12 w-12 text-gray-400" />
                        </div>
                      )}

                      {/* Status Badge */}
                      <div className="absolute top-3 left-3">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(property.offer)}`}>
                          {property.offer.replace('_', ' ').toUpperCase()}
                        </span>
                      </div>

                      {/* Publish Status */}
                      <div className="absolute top-3 right-3">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          property.publishedAt ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {property.publishedAt ? 'Published' : 'Draft'}
                        </span>
                      </div>
                    </div>

                    {/* Property Details */}
                    <div className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="font-semibold text-gray-900 text-lg line-clamp-1">
                          {property.title}
                        </h3>
                        <PropertyDropdownMenu property={property} />
                      </div>

                      <div className="flex items-center text-gray-600 mb-2">
                        <MapPin className="h-4 w-4 mr-1" />
                        <span className="text-sm">{property.city}, {property.country}</span>
                      </div>

                      {/* Neighborhoods */}
                      {(() => {
                        const neighborhoods = normalizeNeighborhoods(property.neighborhood);
                        return neighborhoods.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-2">
                            {neighborhoods.slice(0, 2).map((neighborhood, index) => (
                              <span
                                key={index}
                                className="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs"
                              >
                                <Globe className="h-3 w-3 mr-1" />
                                {neighborhood.name}
                              </span>
                            ))}
                            {neighborhoods.length > 2 && (
                              <span className="inline-flex items-center px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs">
                                +{neighborhoods.length - 2} more
                              </span>
                            )}
                          </div>
                        );
                      })()}

                      <div className="flex items-center text-blue-600 font-semibold mb-3">
                        <DollarSign className="h-4 w-4 mr-1" />
                        <span>{property.price?.toLocaleString()} {property.currency}</span>
                      </div>

                      {/* Property Features */}
                      <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                        <div className="flex items-center">
                          <Bed className="h-4 w-4 mr-1" />
                          {property.bedrooms}
                        </div>
                        <div className="flex items-center">
                          <Bath className="h-4 w-4 mr-1" />
                          {property.bathrooms}
                        </div>
                        <div className="flex items-center">
                          <Square className="h-4 w-4 mr-1" />
                          {property.area} m²
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center space-x-2">
                        <Link
                          href={`/properties/${property.id}`}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Link>
                        <Link
                          href={`/dashboard/properties/${property.id}/edit`}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                        >
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Link>
                        <button
                          onClick={() => handleTogglePublish(property)}
                          className={`px-3 py-2 text-sm rounded-lg ${
                            property.publishedAt
                              ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                              : 'bg-green-100 text-green-800 hover:bg-green-200'
                          }`}
                        >
                          {property.publishedAt ? 'Unpublish' : 'Publish'}
                        </button>
                        <button
                          onClick={() => handleDelete(property.id)}
                          className="px-3 py-2 text-sm bg-red-100 text-red-800 rounded-lg hover:bg-red-200"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            /* List View */
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
              <div className="divide-y divide-gray-200">
                {filteredProperties.map((property) => (
                  <div key={property.id} className="p-6 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-4">
                      {/* Property Image */}
                      <div className="flex-shrink-0 w-24 h-24 bg-gray-200 rounded-lg overflow-hidden">
                        {property.images && property.images.length > 0 ? (
                          <img
                            src={property.images[0].url}
                            alt={property.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Building2 className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                      </div>

                      {/* Property Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-gray-900 truncate">
                              {property.title}
                            </h3>
                            <div className="flex items-center text-gray-600 mt-1">
                              <MapPin className="h-4 w-4 mr-1" />
                              <span className="text-sm">{property.city}, {property.country}</span>
                            </div>

                            {/* Neighborhoods in List View */}
                            {(() => {
                              const neighborhoods = normalizeNeighborhoods(property.neighborhood);
                              return neighborhoods.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-1">
                                  {neighborhoods.slice(0, 3).map((neighborhood, index) => (
                                    <span
                                      key={index}
                                      className="inline-flex items-center px-2 py-1 bg-blue-50 text-blue-700 rounded-full text-xs"
                                    >
                                      <Globe className="h-3 w-3 mr-1" />
                                      {neighborhood.name}
                                    </span>
                                  ))}
                                  {neighborhoods.length > 3 && (
                                    <span className="inline-flex items-center px-2 py-1 bg-blue-50 text-blue-700 rounded-full text-xs">
                                      +{neighborhoods.length - 3}
                                    </span>
                                  )}
                                </div>
                              );
                            })()}

                            <div className="flex items-center text-blue-600 font-semibold mt-2">
                              <DollarSign className="h-4 w-4 mr-1" />
                              <span>{property.price?.toLocaleString()} {property.currency}</span>
                            </div>
                          </div>

                          {/* Status and Actions */}
                          <div className="flex items-center space-x-3 ml-4">
                            {/* Property Features */}
                            <div className="hidden md:flex items-center space-x-4 text-sm text-gray-600">
                              <div className="flex items-center">
                                <Bed className="h-4 w-4 mr-1" />
                                {property.bedrooms}
                              </div>
                              <div className="flex items-center">
                                <Bath className="h-4 w-4 mr-1" />
                                {property.bathrooms}
                              </div>
                              <div className="flex items-center">
                                <Square className="h-4 w-4 mr-1" />
                                {property.area} m²
                              </div>
                            </div>

                            {/* Status Badges */}
                            <div className="flex flex-col space-y-1">
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(property.offer)}`}>
                                {property.offer.replace('_', ' ').toUpperCase()}
                              </span>
                              <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                                property.publishedAt ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {property.publishedAt ? 'Published' : 'Draft'}
                              </span>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex items-center space-x-2">
                              <Link
                                href={`/properties/${property.id}`}
                                className="inline-flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                View
                              </Link>
                              <Link
                                href={`/dashboard/properties/${property.id}/edit`}
                                className="inline-flex items-center px-3 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700"
                              >
                                <Edit className="h-4 w-4 mr-1" />
                                Edit
                              </Link>
                              <PropertyDropdownMenu property={property} />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )
        ) : (
          <div className="text-center py-12">
            <Building2 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {properties.length === 0 ? 'No properties yet' : 'No properties match your filters'}
            </h3>
            <p className="text-gray-500 mb-6">
              {properties.length === 0 
                ? 'Get started by adding your first property listing.'
                : 'Try adjusting your search criteria or filters.'
              }
            </p>
            {properties.length === 0 && (
              <Link
                href="/submit-property"
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                <Plus className="h-5 w-5 mr-2" />
                Add Your First Property
              </Link>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default MyPropertiesPage;
