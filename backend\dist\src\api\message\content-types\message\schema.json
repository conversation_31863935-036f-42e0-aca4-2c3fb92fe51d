{"kind": "collectionType", "collectionName": "messages", "info": {"singularName": "message", "pluralName": "messages", "displayName": "Message", "description": "User-to-user messaging system"}, "options": {"draftAndPublish": false}, "attributes": {"subject": {"type": "string", "required": true}, "content": {"type": "text", "required": true}, "sender": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "sentMessages"}, "recipient": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "receivedMessages"}, "property": {"type": "relation", "relation": "manyToOne", "target": "api::property.property"}, "project": {"type": "relation", "relation": "manyToOne", "target": "api::project.project"}, "isRead": {"type": "boolean", "default": false}, "readAt": {"type": "datetime"}, "messageType": {"type": "enumeration", "enum": ["inquiry", "general", "property-inquiry", "project-inquiry"], "default": "general"}, "parentMessage": {"type": "relation", "relation": "manyToOne", "target": "api::message.message", "inversedBy": "replies"}, "replies": {"type": "relation", "relation": "oneToMany", "target": "api::message.message", "mappedBy": "parentMessage"}, "attachments": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files"]}}}