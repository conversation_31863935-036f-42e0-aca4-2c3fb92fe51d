{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/admin/translations/uk.json.mjs"], "sourcesContent": ["var uk = {\n    \"BoundRoute.title\": \"Пов'язано з\",\n    \"EditForm.inputSelect.description.role\": \"Підключає нового автентифікованого користувача до вибраної ролі.\",\n    \"EditForm.inputSelect.label.role\": \"Роль за замовчуванням для автентифікованих користувачів\",\n    \"EditForm.inputToggle.description.email\": \"Не дозволяти користувачам створювати кілька аккаунтів з тим самим email, але різним провайдером автентифікації.\",\n    \"EditForm.inputToggle.description.email-confirmation\": \"Якщо увімкнено (ON), щойно зареєстровані користувачі отримують листа для підтверждення.\",\n    \"EditForm.inputToggle.description.email-confirmation-redirection\": \"Куди ви будете перенаправлені після підтвердження свого email.\",\n    \"EditForm.inputToggle.description.email-reset-password\": \"URL-адреса сторінки скидання пароля вашого додатку\",\n    \"EditForm.inputToggle.description.sign-up\": \"Якщо вимкнено (OFF), реєстрація заборонена. Незалежно від використовуваного провайдера більше ніхто не зможе приєднатись.\",\n    \"EditForm.inputToggle.label.email\": \"Один аккаунт на email\",\n    \"EditForm.inputToggle.label.email-confirmation\": \"Увімкнути підтверження email\",\n    \"EditForm.inputToggle.label.email-confirmation-redirection\": \"URL для перенаправлення\",\n    \"EditForm.inputToggle.label.email-reset-password\": \"Сторінка скидання пароля\",\n    \"EditForm.inputToggle.label.sign-up\": \"Увімкнути реєстрацію\",\n    \"EditForm.inputToggle.placeholder.email-confirmation-redirection\": \"наприклад: https://yourfrontend.com/email-confirmation-redirection\",\n    \"EditForm.inputToggle.placeholder.email-reset-password\": \"наприклад: https://yourfrontend.com/reset-password\",\n    \"EditPage.form.roles\": \"Деталі ролі\",\n    \"Email.template.data.loaded\": \"Шаблони електронної пошти завантажено\",\n    \"Email.template.email_confirmation\": \"Підтвердження електронної адреси\",\n    \"Email.template.form.edit.label\": \"Редагувати шаблон\",\n    \"Email.template.table.action.label\": \"дія\",\n    \"Email.template.table.icon.label\": \"іконка\",\n    \"Email.template.table.name.label\": \"назва\",\n    \"Form.advancedSettings.data.loaded\": \"Дані розширених налаштувань завантажено\",\n    \"HeaderNav.link.advancedSettings\": \"Розширені налаштування\",\n    \"HeaderNav.link.emailTemplates\": \"Шаблони листів\",\n    \"HeaderNav.link.providers\": \"Провайдери\",\n    \"Plugin.permissions.plugins.description\": \"Встановіть всі дозволені дії у плаґіні {name}.\",\n    \"Plugins.header.description\": \"Нижче перераховано лише дії, пов'язані з маршрутом.\",\n    \"Plugins.header.title\": \"Дозволи\",\n    \"Policies.header.hint\": \"Виберіть дії вашого додатку або дії плаґіну та натисніть на значок шестірні, щоб відобразити пов'язаний маршрут\",\n    \"Policies.header.title\": \"Розширені налашрування\",\n    \"PopUpForm.Email.email_templates.inputDescription\": \"Якщо ви не впевнені, як використовувати змінні, {link}\",\n    \"PopUpForm.Email.link.documentation\": \"перегляньте документацію.\",\n    \"PopUpForm.Email.options.from.email.label\": \"Email відправника\",\n    \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Email.options.from.name.label\": \"Ім'я відправника\",\n    \"PopUpForm.Email.options.from.name.placeholder\": \"Тарас Шевченко\",\n    \"PopUpForm.Email.options.message.label\": \"Повідомлення\",\n    \"PopUpForm.Email.options.object.label\": \"Тема\",\n    \"PopUpForm.Email.options.object.placeholder\": \"Будь ласка, підтвердьте свою електронну адресу для %APP_NAME%\",\n    \"PopUpForm.Email.options.response_email.label\": \"Email для відповіді\",\n    \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n    \"PopUpForm.Providers.enabled.description\": \"Якщо вимкнуто, користувачі не зможуть вікористати цей провайдер.\",\n    \"PopUpForm.Providers.enabled.label\": \"Увімкнути\",\n    \"PopUpForm.Providers.key.label\": \"Client ID\",\n    \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.redirectURL.front-end.label\": \"URL переадресації для вашего front-end додатку\",\n    \"PopUpForm.Providers.redirectURL.label\": \"URL -адреса для переадресації, щоб додати у ваші конфігурації програми {постачальник}\",\n    \"PopUpForm.Providers.secret.label\": \"Client Secret\",\n    \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n    \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n    \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n    \"PopUpForm.header.edit.email-templates\": \"Редагування шаблону листів\",\n    \"PopUpForm.header.edit.providers\": \"Редагувати провайдера\",\n    \"Providers.data.loaded\": \"Провайдери завантажено\",\n    \"Providers.status\": \"Статус\",\n    \"Roles.empty\": \"У вас ще немає ролей.\",\n    \"Roles.empty.search\": \"Не знайдено ролей, що відповідають пошуку.\",\n    \"Settings.roles.deleted\": \"Роль видалено\",\n    \"Settings.roles.edited\": \"Роль редаговано\",\n    \"Settings.section-label\": \"Плаґін користувачів та дозволів\",\n    \"components.Input.error.validation.email\": \"Це неправильна електронна адреса\",\n    \"components.Input.error.validation.json\": \"Це не відповідає формату JSON\",\n    \"components.Input.error.validation.max\": \"Значення занадто велике.\",\n    \"components.Input.error.validation.maxLength\": \"Значення занадто довге.\",\n    \"components.Input.error.validation.min\": \"Значення занадто низьке.\",\n    \"components.Input.error.validation.minLength\": \"Значення занадто коротке.\",\n    \"components.Input.error.validation.minSupMax\": \"Не може бути більшим\",\n    \"components.Input.error.validation.regex\": \"Значення не відповідає регулярному виразу.\",\n    \"components.Input.error.validation.required\": \"Це значення є обов’язковим.\",\n    \"components.Input.error.validation.unique\": \"Це значення вже використовується.\",\n    \"notification.success.submit\": \"Налаштування оновлено\",\n    \"page.title\": \"Налаштування - Ролі\",\n    \"plugin.description.long\": \"Захистіть API за допомогою процесу аутентифікації на основі JWT. Цей плаґін також включає можливості ACL, які дозволяють керувати дозволами між групами користувачів.\",\n    \"plugin.description.short\": \"Захистіть API за допомогою процесу аутентифікації на основі JWT\",\n    \"plugin.name\": \"Плаґін користувачів та дозволів\",\n    \"popUpWarning.button.cancel\": \"Скасувати\",\n    \"popUpWarning.button.confirm\": \"Підтвердити\",\n    \"popUpWarning.title\": \"Будь ласка, підтвердіть\",\n    \"popUpWarning.warning.cancel\": \"Ви впевнені, що хочете скасувати свої зміни?\"\n};\n\nexport { uk as default };\n//# sourceMappingURL=uk.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,cAAc;AAAA,EACd,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,+BAA+B;AACnC;", "names": []}