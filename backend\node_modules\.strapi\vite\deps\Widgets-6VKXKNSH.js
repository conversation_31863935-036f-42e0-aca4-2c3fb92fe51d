import {
  DocumentStatus,
  RelativeTime
} from "./chunk-JIJGBBK6.js";
import {
  contentManagerApi
} from "./chunk-FXFI7RPJ.js";
import "./chunk-2I627FPA.js";
import "./chunk-YL4HUWI5.js";
import {
  Widget
} from "./chunk-FEMTQTUW.js";
import "./chunk-4QC3H4VA.js";
import "./chunk-55VWGVZH.js";
import "./chunk-OZACAF6N.js";
import "./chunk-WTS7FMHT.js";
import "./chunk-WZFBEA4P.js";
import "./chunk-JMRGINWF.js";
import "./chunk-AOLLD5YI.js";
import "./chunk-QHNDELYW.js";
import "./chunk-TNSPRZER.js";
import "./chunk-53NOEIC2.js";
import "./chunk-5VB7WV2W.js";
import "./chunk-O5T5P624.js";
import "./chunk-UPTQLDPC.js";
import "./chunk-RXZGDL2L.js";
import "./chunk-XTOANYCB.js";
import "./chunk-MBK4V2X7.js";
import "./chunk-HMK5KCER.js";
import "./chunk-K65KIEAL.js";
import "./chunk-5ZAWHNOU.js";
import "./chunk-4S663EA7.js";
import "./chunk-KTBXM7CI.js";
import "./chunk-2H2OFBN6.js";
import "./chunk-J2R7FCFP.js";
import "./chunk-IFOFBKTA.js";
import "./chunk-D6TU5LYD.js";
import "./chunk-EGNP2T5O.js";
import {
  useTracking
} from "./chunk-VY2UWIU2.js";
import "./chunk-RFPGJ5TL.js";
import "./chunk-YXDCVYVT.js";
import "./chunk-QIJGNK42.js";
import "./chunk-NTAW33Y7.js";
import "./chunk-3BMMADJT.js";
import "./chunk-SOYUVSKZ.js";
import "./chunk-M4ANT4X2.js";
import "./chunk-BEIN36ZT.js";
import "./chunk-7KQP2N4I.js";
import "./chunk-2LW7YCMH.js";
import "./chunk-OBYG3BLK.js";
import "./chunk-D4WYVNVM.js";
import "./chunk-MMOBCIZG.js";
import "./chunk-57N2SCQG.js";
import "./chunk-I33OYNGL.js";
import "./chunk-PQINNV4N.js";
import "./chunk-VYSYYPOB.js";
import "./chunk-TKNBHXFW.js";
import "./chunk-HAGJJFVR.js";
import "./chunk-2AM3SIJO.js";
import "./chunk-37UIT6AH.js";
import "./chunk-7DRT6PFJ.js";
import "./chunk-BHLYCXQ7.js";
import "./chunk-BLJUR3JO.js";
import "./chunk-CE4VABH2.js";
import "./chunk-QOUV5O5E.js";
import "./chunk-F77XFYRA.js";
import {
  Box,
  IconButton,
  Table,
  Tbody,
  Td,
  Tr,
  Typography,
  useIntl
} from "./chunk-Z7NCMQQT.js";
import "./chunk-5ZC4PE57.js";
import {
  Link,
  useNavigate
} from "./chunk-S65ZWNEO.js";
import "./chunk-FOD4ENRR.js";
import {
  ForwardRef$1v
} from "./chunk-3EMSRRPD.js";
import {
  require_jsx_runtime
} from "./chunk-NIAJZ5MX.js";
import {
  dt
} from "./chunk-3CQBCJ3G.js";
import "./chunk-MADUDGYZ.js";
import {
  __toESM
} from "./chunk-PLDDJCW6.js";

// node_modules/@strapi/content-manager/dist/admin/components/Widgets.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);

// node_modules/@strapi/content-manager/dist/admin/services/homepage.mjs
var homepageService = contentManagerApi.enhanceEndpoints({
  addTagTypes: [
    "RecentDocumentList"
  ]
}).injectEndpoints({
  /**
   * TODO: Remove overrideExisting when we remove the future flag
   * and delete the old homepage service in the admin
   */
  overrideExisting: true,
  endpoints: (builder) => ({
    getRecentDocuments: builder.query({
      query: (params) => `/content-manager/homepage/recent-documents?action=${params.action}`,
      transformResponse: (response) => response.data,
      providesTags: (res, _err, { action }) => [
        {
          type: "RecentDocumentList",
          id: action
        }
      ]
    })
  })
});
var { useGetRecentDocumentsQuery } = homepageService;

// node_modules/@strapi/content-manager/dist/admin/components/Widgets.mjs
var CellTypography = dt(Typography).attrs({
  maxWidth: "14.4rem",
  display: "block"
})`
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;
var RecentDocumentsTable = ({ documents }) => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const navigate = useNavigate();
  const getEditViewLink = (document) => {
    const isSingleType = document.kind === "singleType";
    const kindPath = isSingleType ? "single-types" : "collection-types";
    const queryParams = document.locale ? `?plugins[i18n][locale]=${document.locale}` : "";
    return `/content-manager/${kindPath}/${document.contentTypeUid}${isSingleType ? "" : "/" + document.documentId}${queryParams}`;
  };
  const handleRowClick = (document) => () => {
    trackUsage("willEditEntryFromHome");
    const link = getEditViewLink(document);
    navigate(link);
  };
  return (0, import_jsx_runtime.jsx)(Table, {
    colCount: 5,
    rowCount: (documents == null ? void 0 : documents.length) ?? 0,
    children: (0, import_jsx_runtime.jsx)(Tbody, {
      children: documents == null ? void 0 : documents.map((document) => (0, import_jsx_runtime.jsxs)(Tr, {
        onClick: handleRowClick(document),
        cursor: "pointer",
        children: [
          (0, import_jsx_runtime.jsx)(Td, {
            children: (0, import_jsx_runtime.jsx)(CellTypography, {
              title: document.title,
              variant: "omega",
              textColor: "neutral800",
              children: document.title
            })
          }),
          (0, import_jsx_runtime.jsx)(Td, {
            children: (0, import_jsx_runtime.jsx)(CellTypography, {
              variant: "omega",
              textColor: "neutral600",
              children: document.kind === "singleType" ? formatMessage({
                id: "content-manager.widget.last-edited.single-type",
                defaultMessage: "Single-Type"
              }) : formatMessage({
                id: document.contentTypeDisplayName,
                defaultMessage: document.contentTypeDisplayName
              })
            })
          }),
          (0, import_jsx_runtime.jsx)(Td, {
            children: (0, import_jsx_runtime.jsx)(Box, {
              display: "inline-block",
              children: document.status ? (0, import_jsx_runtime.jsx)(DocumentStatus, {
                status: document.status
              }) : (0, import_jsx_runtime.jsx)(Typography, {
                textColor: "neutral600",
                "aria-hidden": true,
                children: "-"
              })
            })
          }),
          (0, import_jsx_runtime.jsx)(Td, {
            children: (0, import_jsx_runtime.jsx)(Typography, {
              textColor: "neutral600",
              children: (0, import_jsx_runtime.jsx)(RelativeTime, {
                timestamp: new Date(document.updatedAt)
              })
            })
          }),
          (0, import_jsx_runtime.jsx)(Td, {
            onClick: (e) => e.stopPropagation(),
            children: (0, import_jsx_runtime.jsx)(Box, {
              display: "inline-block",
              children: (0, import_jsx_runtime.jsx)(IconButton, {
                tag: Link,
                to: getEditViewLink(document),
                onClick: () => trackUsage("willEditEntryFromHome"),
                label: formatMessage({
                  id: "content-manager.actions.edit.label",
                  defaultMessage: "Edit"
                }),
                variant: "ghost",
                children: (0, import_jsx_runtime.jsx)(ForwardRef$1v, {})
              })
            })
          })
        ]
      }, document.documentId))
    })
  });
};
var LastEditedWidget = () => {
  const { formatMessage } = useIntl();
  const { data, isLoading, error } = useGetRecentDocumentsQuery({
    action: "update"
  });
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Widget.Loading, {});
  }
  if (error || !data) {
    return (0, import_jsx_runtime.jsx)(Widget.Error, {});
  }
  if (data.length === 0) {
    return (0, import_jsx_runtime.jsx)(Widget.NoData, {
      children: formatMessage({
        id: "content-manager.widget.last-edited.no-data",
        defaultMessage: "No edited entries"
      })
    });
  }
  return (0, import_jsx_runtime.jsx)(RecentDocumentsTable, {
    documents: data
  });
};
var LastPublishedWidget = () => {
  const { formatMessage } = useIntl();
  const { data, isLoading, error } = useGetRecentDocumentsQuery({
    action: "publish"
  });
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Widget.Loading, {});
  }
  if (error || !data) {
    return (0, import_jsx_runtime.jsx)(Widget.Error, {});
  }
  if (data.length === 0) {
    return (0, import_jsx_runtime.jsx)(Widget.NoData, {
      children: formatMessage({
        id: "content-manager.widget.last-published.no-data",
        defaultMessage: "No published entries"
      })
    });
  }
  return (0, import_jsx_runtime.jsx)(RecentDocumentsTable, {
    documents: data
  });
};
export {
  LastEditedWidget,
  LastPublishedWidget
};
//# sourceMappingURL=Widgets-6VKXKNSH.js.map
