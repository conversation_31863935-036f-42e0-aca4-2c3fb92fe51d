import "./chunk-PLDDJCW6.js";

// node_modules/@strapi/i18n/dist/admin/translations/zh.json.mjs
var zh = {
  "CMEditViewCopyLocale.copy-failure": "無法複製地區設定",
  "CMEditViewCopyLocale.copy-success": "地區設定已複製",
  "CMEditViewCopyLocale.copy-text": "從其他地區設定填入",
  "CMEditViewCopyLocale.submit-text": "是的，請填入",
  "CMListView.popover.display-locales.label": "顯示已翻譯的地區設定",
  "CheckboxConfirmation.Modal.body": "您確定要停用嗎？",
  "CheckboxConfirmation.Modal.button-confirm": "是的，請停用",
  "CheckboxConfirmation.Modal.content": "停用本地化將刪除您的所有內容，與預設地區設定關聯的內容除外 (若存在)。",
  "Field.localized": "此欄位對所選的地區設定來說是唯一的",
  "Field.not-localized": "此欄位在所有地區設定間皆通用",
  "Settings.list.actions.add": "新增地區設定",
  "Settings.list.actions.delete": "刪除地區設定",
  "Settings.list.actions.deleteAdditionalInfos": "這將刪除已啟用的地區設定版本 <em>(from Internationalization)</em>",
  "Settings.list.actions.edit": "編輯地區設定",
  "Settings.list.description": "設定國際化外掛程式",
  "Settings.list.empty.description": "此行為不尋常，這表示您最終仍手動修改了資料庫。請至少在您的資料庫中儲存一種地區設定以正常使用 Strapi。",
  "Settings.list.empty.title": "無地區設定。",
  "Settings.locales.default": "預設",
  "Settings.locales.list.sort.default": "按預設地區設定排序",
  "Settings.locales.list.sort.displayName": "按顯示名稱排序",
  "Settings.locales.list.sort.id": "按 ID 排序",
  "Settings.locales.modal.advanced": "進階設定",
  "Settings.locales.modal.advanced.setAsDefault": "設為預設地區設定",
  "Settings.locales.modal.advanced.setAsDefault.hint": "必須有一個預設的地區設定，選擇其他地區設定以更改",
  "Settings.locales.modal.advanced.settings": "設定",
  "Settings.locales.modal.base": "基本設定",
  "Settings.locales.modal.create.alreadyExist": "此地區設定已存在",
  "Settings.locales.modal.create.defaultLocales.loading": "正在載入可用的地區設定...",
  "Settings.locales.modal.create.success": "已成功新增地區設定",
  "Settings.locales.modal.create.tab.label": "在 I18N 基礎和進階設定間瀏覽",
  "Settings.locales.modal.delete.confirm": "是的，請刪除",
  "Settings.locales.modal.delete.message": "刪除此地區設定將刪除所有與其關聯的內容。如果您想要保留內容，請先重新配置給其他地區設定。",
  "Settings.locales.modal.delete.secondMessage": "您確定要刪除此地區設定嗎？",
  "Settings.locales.modal.delete.success": "已成功刪除地區設定",
  "Settings.locales.modal.edit.confirmation": "完成",
  "Settings.locales.modal.create.code.label": "地區設定",
  "Settings.locales.modal.edit.success": "已成功編輯地區設定",
  "Settings.locales.modal.edit.tab.label": "在 I18N 基礎和進階設定間瀏覽",
  "Settings.locales.modal.create.name.label": "地區設定顯示名稱",
  "Settings.locales.modal.create.name.label.description": "地區設定將以該名稱在管理面板中顯示",
  "Settings.locales.modal.create.name.label.error": "地區設定顯示名稱不得超過 50 個字元。",
  "Settings.locales.modal.locales.label": "地區設定",
  "Settings.locales.modal.locales.loaded": "地區設定已成功載入。",
  "Settings.locales.modal.title": "設定",
  "Settings.locales.row.default-locale": "預設地區設定",
  "Settings.locales.row.displayName": "顯示名稱",
  "Settings.locales.row.id": "ID",
  "Settings.permissions.loading": "正在載入權限",
  "Settings.permissions.read.denied.description": "若要讀取此內容，請聯絡系統管理員。",
  "Settings.permissions.read.denied.title": "您沒有存取此內容的權限。",
  "actions.select-locale": "選擇地區設定",
  "components.Select.locales.not-available": "無可用內容",
  "plugin.description.long": "此外掛程式允許您從管理面板和 API 建立、讀取、更新不同語言的內容。",
  "plugin.description.short": "此外掛程式允許您從管理面板和 API 建立、讀取、更新不同語言的內容。",
  "plugin.name": "國際化",
  "plugin.schema.i18n.localized.description-content-type": "讓您的內容有多種地區設定",
  "plugin.schema.i18n.localized.description-field": "此欄位在各個地區設定中可以有不同數值",
  "plugin.schema.i18n.localized.label-content-type": "為此內容型別啟用本地化",
  "plugin.schema.i18n.localized.label-field": "為此欄位啟用本地化"
};
export {
  zh as default
};
//# sourceMappingURL=zh.json-32A62ZT5.js.map
