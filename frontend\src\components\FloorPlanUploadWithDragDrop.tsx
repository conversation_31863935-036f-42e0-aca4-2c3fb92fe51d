'use client';

import React, { useState, useRef } from 'react';
import { Upload, X, FileText, Image as ImageIcon } from 'lucide-react';

interface FloorPlanUploadWithDragDropProps {
  onFloorPlanChange: (file: File | null) => void;
  className?: string;
  currentFloorPlan?: File | null;
}

const FloorPlanUploadWithDragDrop: React.FC<FloorPlanUploadWithDragDropProps> = ({
  onFloorPlanChange,
  className = '',
  currentFloorPlan
}) => {
  const [floorPlan, setFloorPlan] = useState<File | null>(currentFloorPlan || null);
  const [dragOver, setDragOver] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const isImageFile = (file: File) => file.type.startsWith('image/');
  const isPdfFile = (file: File) => file.type === 'application/pdf';

  const handleFile = (file: File) => {
    if (isImageFile(file) || isPdfFile(file)) {
      setFloorPlan(file);
      onFloorPlanChange(file);
      
      // Create preview for images
      if (isImageFile(file)) {
        const previewUrl = URL.createObjectURL(file);
        setPreview(previewUrl);
      } else {
        setPreview(null);
      }
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFile(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFile(file);
    }
  };

  const removeFloorPlan = () => {
    setFloorPlan(null);
    onFloorPlanChange(null);
    if (preview) {
      URL.revokeObjectURL(preview);
      setPreview(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-700 mb-3">
        Floor Plan
      </label>

      {!floorPlan ? (
        // Upload Area
        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={() => fileInputRef.current?.click()}
          className={`relative border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all ${
            dragOver
              ? 'border-blue-500 bg-blue-50'
              : 'border-gray-300 hover:border-gray-400 hover:bg-gray-50'
          }`}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*,.pdf"
            onChange={handleFileInput}
            className="hidden"
          />
          
          <Upload className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          
          <p className="text-lg font-medium text-gray-700 mb-2">
            Drop floor plan here or click to browse
          </p>
          <p className="text-sm text-gray-500 mb-4">
            Upload your property floor plan
          </p>
          <p className="text-xs text-gray-400">
            Supported formats: PNG, JPG, GIF, PDF • Max size: 10MB
          </p>
        </div>
      ) : (
        // Floor Plan Preview
        <div className="border-2 border-gray-200 rounded-lg p-4">
          <div className="flex items-start space-x-4">
            {/* File Icon/Preview */}
            <div className="flex-shrink-0">
              {preview ? (
                <div className="w-20 h-20 rounded-lg overflow-hidden bg-gray-100">
                  <img
                    src={preview}
                    alt="Floor plan preview"
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="w-20 h-20 rounded-lg bg-red-50 flex items-center justify-center">
                  <FileText className="h-8 w-8 text-red-500" />
                </div>
              )}
            </div>
            
            {/* File Info */}
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {floorPlan.name}
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {formatFileSize(floorPlan.size)} • {floorPlan.type}
              </p>
              <div className="mt-2">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  {isImageFile(floorPlan) ? (
                    <>
                      <ImageIcon className="h-3 w-3 mr-1" />
                      Image
                    </>
                  ) : (
                    <>
                      <FileText className="h-3 w-3 mr-1" />
                      PDF
                    </>
                  )}
                </span>
              </div>
            </div>
            
            {/* Remove Button */}
            <button
              onClick={removeFloorPlan}
              className="flex-shrink-0 p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors"
              title="Remove floor plan"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          {/* Replace Button */}
          <div className="mt-4 pt-4 border-t border-gray-200">
            <button
              onClick={() => fileInputRef.current?.click()}
              className="text-sm text-blue-600 hover:text-blue-800 font-medium"
            >
              Replace floor plan
            </button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*,.pdf"
              onChange={handleFileInput}
              className="hidden"
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default FloorPlanUploadWithDragDrop;
