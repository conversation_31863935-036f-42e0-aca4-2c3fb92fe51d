const fetch = require('node-fetch');

const STRAPI_URL = 'http://localhost:1337';

async function runComprehensiveTest() {
  console.log('🚀 Starting comprehensive property edit test...\n');

  try {
    // Login
    console.log('🔐 Logging in...');
    const loginResponse = await fetch(`${STRAPI_URL}/api/auth/local`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        identifier: '<EMAIL>',
        password: 'Mb123321'
      })
    });

    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginResponse.status}`);
    }

    const loginData = await loginResponse.json();
    const jwt = loginData.jwt;
    console.log('✅ Login successful\n');

    // Get user properties
    console.log('📋 Getting user properties...');
    const propertiesResponse = await fetch(`${STRAPI_URL}/api/properties/my-properties`, {
      headers: { 'Authorization': `Bearer ${jwt}` }
    });

    if (!propertiesResponse.ok) {
      throw new Error(`Failed to get properties: ${propertiesResponse.status}`);
    }

    const propertiesData = await propertiesResponse.json();
    const properties = propertiesData.data;
    console.log(`Found ${properties.length} properties`);

    if (properties.length === 0) {
      console.log('❌ No properties found for testing');
      return;
    }

    const testProperty = properties[0];
    console.log(`Testing with property: ${testProperty.title} (ID: ${testProperty.id}, DocumentId: ${testProperty.documentId})\n`);

    // Test 1: getForEdit with documentId
    console.log('🔍 Test 1: getForEdit with documentId...');
    const editResponse1 = await fetch(`${STRAPI_URL}/api/properties/${testProperty.documentId}/edit`, {
      headers: { 'Authorization': `Bearer ${jwt}` }
    });

    if (editResponse1.ok) {
      const editData1 = await editResponse1.json();
      console.log('✅ getForEdit with documentId successful');
      console.log(`   Property title: ${editData1.data.title}`);
      console.log(`   Property ID: ${editData1.data.id}`);
      console.log(`   Document ID: ${editData1.data.documentId}\n`);
    } else {
      console.log(`❌ getForEdit with documentId failed: ${editResponse1.status}\n`);
    }

    // Test 2: getForEdit with numeric ID
    console.log('🔍 Test 2: getForEdit with numeric ID...');
    const editResponse2 = await fetch(`${STRAPI_URL}/api/properties/${testProperty.id}/edit`, {
      headers: { 'Authorization': `Bearer ${jwt}` }
    });

    if (editResponse2.ok) {
      const editData2 = await editResponse2.json();
      console.log('✅ getForEdit with numeric ID successful');
      console.log(`   Property title: ${editData2.data.title}`);
      console.log(`   Property ID: ${editData2.data.id}`);
      console.log(`   Document ID: ${editData2.data.documentId}\n`);
    } else {
      console.log(`❌ getForEdit with numeric ID failed: ${editResponse2.status}\n`);
    }

    // Test 3: Update with documentId
    console.log('✏️ Test 3: Update with documentId...');
    const updateData1 = {
      data: {
        title: testProperty.title + ' - Updated via DocumentId',
        description: 'Updated description via documentId'
      }
    };

    const updateResponse1 = await fetch(`${STRAPI_URL}/api/properties/${testProperty.documentId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwt}`
      },
      body: JSON.stringify(updateData1)
    });

    if (updateResponse1.ok) {
      const updateResult1 = await updateResponse1.json();
      console.log('✅ Update with documentId successful');
      console.log(`   Updated title: ${updateResult1.data?.title || 'No title in response'}`);
      console.log(`   Response has data: ${updateResult1.data ? 'Yes' : 'No'}`);
      if (updateResult1.data) {
        console.log(`   Property ID: ${updateResult1.data.id}`);
        console.log(`   Document ID: ${updateResult1.data.documentId}`);
      }
      console.log();
    } else {
      const errorData1 = await updateResponse1.json();
      console.log(`❌ Update with documentId failed: ${updateResponse1.status}`);
      console.log(`   Error: ${errorData1.error?.message || 'Unknown error'}\n`);
    }

    // Test 4: Update with numeric ID
    console.log('✏️ Test 4: Update with numeric ID...');
    const updateData2 = {
      data: {
        title: testProperty.title + ' - Updated via Numeric ID',
        description: 'Updated description via numeric ID'
      }
    };

    const updateResponse2 = await fetch(`${STRAPI_URL}/api/properties/${testProperty.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwt}`
      },
      body: JSON.stringify(updateData2)
    });

    if (updateResponse2.ok) {
      const updateResult2 = await updateResponse2.json();
      console.log('✅ Update with numeric ID successful');
      console.log(`   Updated title: ${updateResult2.data?.title || 'No title in response'}`);
      console.log(`   Response has data: ${updateResult2.data ? 'Yes' : 'No'}`);
      if (updateResult2.data) {
        console.log(`   Property ID: ${updateResult2.data.id}`);
        console.log(`   Document ID: ${updateResult2.data.documentId}`);
      }
      console.log();
    } else {
      const errorData2 = await updateResponse2.json();
      console.log(`❌ Update with numeric ID failed: ${updateResponse2.status}`);
      console.log(`   Error: ${errorData2.error?.message || 'Unknown error'}\n`);
    }

    // Test 5: Verify final state
    console.log('🔍 Test 5: Verify final property state...');
    const verifyResponse = await fetch(`${STRAPI_URL}/api/properties/${testProperty.documentId}/edit`, {
      headers: { 'Authorization': `Bearer ${jwt}` }
    });

    if (verifyResponse.ok) {
      const verifyData = await verifyResponse.json();
      console.log('✅ Final verification successful');
      console.log(`   Final title: ${verifyData.data.title}`);
      console.log(`   Final description: ${verifyData.data.description || 'No description'}`);
      console.log(`   Property ID: ${verifyData.data.id}`);
      console.log(`   Document ID: ${verifyData.data.documentId}`);
    } else {
      console.log(`❌ Final verification failed: ${verifyResponse.status}`);
    }

    console.log('\n🎉 Comprehensive test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

runComprehensiveTest();
