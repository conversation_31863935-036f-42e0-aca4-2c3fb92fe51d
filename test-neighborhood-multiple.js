// Test script to check if neighborhood field accepts multiple values
const axios = require('axios');

const API_BASE = 'http://localhost:1337/api';

// Existing user 'badr' login credentials
const testUser = {
  email: '<EMAIL>',
  password: 'Mb123321'
};

async function testNeighborhoodMultiple() {
  try {
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/local`, {
      identifier: testUser.email,
      password: testUser.password
    });
    
    const userId = loginResponse.data.user.id;
    const userToken = loginResponse.data.jwt;
    console.log(`✅ Logged in as user ID: ${userId}`);
    
    // Test 1: Single neighborhood value
    console.log('\n🏠 Test 1: Creating property with single neighborhood...');
    const singleNeighborhoodProperty = {
      title: 'Test Single Neighborhood',
      description: 'Testing single neighborhood value',
      price: 100000,
      propertyType: 'apartment',
      area: 1000,
      address: '123 Test Street',
      city: 'Test City',
      country: 'USA',
      neighborhood: 'Downtown'
    };
    
    try {
      const response1 = await axios.post(`${API_BASE}/properties`, {
        data: singleNeighborhoodProperty
      }, {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Single neighborhood property created successfully');
      console.log('Neighborhood value:', response1.data.data.neighborhood);
    } catch (error) {
      console.error('❌ Single neighborhood failed:', error.response?.data || error.message);
    }
    
    // Test 2: Multiple neighborhood values as array
    console.log('\n🏠 Test 2: Creating property with multiple neighborhoods (array)...');
    const multipleNeighborhoodProperty = {
      title: 'Test Multiple Neighborhoods Array',
      description: 'Testing multiple neighborhood values as array',
      price: 150000,
      propertyType: 'villa',
      area: 1500,
      address: '456 Test Avenue',
      city: 'Test City',
      country: 'USA',
      neighborhood: ['Downtown', 'Business District']
    };
    
    try {
      const response2 = await axios.post(`${API_BASE}/properties`, {
        data: multipleNeighborhoodProperty
      }, {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Multiple neighborhoods (array) property created successfully');
      console.log('Neighborhood value:', response2.data.data.neighborhood);
    } catch (error) {
      console.error('❌ Multiple neighborhoods (array) failed:', error.response?.data || error.message);
    }
    
    // Test 3: Multiple neighborhood values as comma-separated string
    console.log('\n🏠 Test 3: Creating property with multiple neighborhoods (comma-separated)...');
    const commaNeighborhoodProperty = {
      title: 'Test Multiple Neighborhoods Comma',
      description: 'Testing multiple neighborhood values as comma-separated',
      price: 200000,
      propertyType: 'townhouse',
      area: 1800,
      address: '789 Test Boulevard',
      city: 'Test City',
      country: 'USA',
      neighborhood: 'Waterfront,Historic District'
    };
    
    try {
      const response3 = await axios.post(`${API_BASE}/properties`, {
        data: commaNeighborhoodProperty
      }, {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Multiple neighborhoods (comma-separated) property created successfully');
      console.log('Neighborhood value:', response3.data.data.neighborhood);
    } catch (error) {
      console.error('❌ Multiple neighborhoods (comma-separated) failed:', error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
  }
}

// Run the test
testNeighborhoodMultiple();
