// Script to populate nearby place categories
const axios = require('axios');
const { GOOGLE_PLACE_TYPES } = require('./backend/src/api/nearby-place-category/services/google-place-types');

const API_BASE = 'http://localhost:1337/api';

// Test user credentials (using existing user)
const userCredentials = {
  email: '<EMAIL>',
  password: 'Mb123321'
};

const defaultCategories = [
  {
    name: 'education',
    displayName: 'Education',
    description: 'Schools, universities, libraries, and educational institutions',
    icon: '🎓',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.SCHOOL,
      GOOGLE_PLACE_TYPES.PRIMARY_SCHOOL,
      GOOGLE_PLACE_TYPES.SECONDARY_SCHOOL,
      GOOGLE_PLACE_TYPES.UNIVERSITY,
      GOOGLE_PLACE_TYPES.LIBRARY
    ],
    enabled: true,
    searchRadius: 2000,
    maxResults: 10,
    priority: 10,
    color: '#10B981'
  },
  {
    name: 'restaurants',
    displayName: 'Restaurants & Food',
    description: 'Restaurants, cafes, bars, and food establishments',
    icon: '🍽️',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.RESTAURANT,
      GOOGLE_PLACE_TYPES.CAFE,
      GOOGLE_PLACE_TYPES.BAR,
      GOOGLE_PLACE_TYPES.BAKERY,
      GOOGLE_PLACE_TYPES.MEAL_DELIVERY,
      GOOGLE_PLACE_TYPES.MEAL_TAKEAWAY
    ],
    enabled: true,
    searchRadius: 1000,
    maxResults: 15,
    priority: 9,
    color: '#F59E0B'
  },
  {
    name: 'shopping',
    displayName: 'Shopping',
    description: 'Shopping malls, stores, supermarkets, and retail',
    icon: '🛍️',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.SHOPPING_MALL,
      GOOGLE_PLACE_TYPES.SUPERMARKET,
      GOOGLE_PLACE_TYPES.CONVENIENCE_STORE,
      GOOGLE_PLACE_TYPES.DEPARTMENT_STORE,
      GOOGLE_PLACE_TYPES.CLOTHING_STORE,
      GOOGLE_PLACE_TYPES.ELECTRONICS_STORE,
      GOOGLE_PLACE_TYPES.STORE
    ],
    enabled: true,
    searchRadius: 1500,
    maxResults: 10,
    priority: 8,
    color: '#8B5CF6'
  },
  {
    name: 'healthcare',
    displayName: 'Healthcare',
    description: 'Hospitals, clinics, pharmacies, and medical facilities',
    icon: '🏥',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.HOSPITAL,
      GOOGLE_PLACE_TYPES.DOCTOR,
      GOOGLE_PLACE_TYPES.DENTIST,
      GOOGLE_PLACE_TYPES.PHARMACY,
      GOOGLE_PLACE_TYPES.PHYSIOTHERAPIST,
      GOOGLE_PLACE_TYPES.VETERINARY_CARE
    ],
    enabled: true,
    searchRadius: 2000,
    maxResults: 8,
    priority: 9,
    color: '#EF4444'
  },
  {
    name: 'transportation',
    displayName: 'Transportation',
    description: 'Bus stops, train stations, airports, and transit hubs',
    icon: '🚌',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.BUS_STATION,
      GOOGLE_PLACE_TYPES.TRAIN_STATION,
      GOOGLE_PLACE_TYPES.SUBWAY_STATION,
      GOOGLE_PLACE_TYPES.LIGHT_RAIL_STATION,
      GOOGLE_PLACE_TYPES.TRANSIT_STATION,
      GOOGLE_PLACE_TYPES.TAXI_STAND,
      GOOGLE_PLACE_TYPES.AIRPORT,
      GOOGLE_PLACE_TYPES.GAS_STATION
    ],
    enabled: true,
    searchRadius: 2000,
    maxResults: 8,
    priority: 7,
    color: '#3B82F6'
  },
  {
    name: 'entertainment',
    displayName: 'Entertainment',
    description: 'Movie theaters, parks, gyms, and recreational facilities',
    icon: '🎬',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.MOVIE_THEATER,
      GOOGLE_PLACE_TYPES.NIGHT_CLUB,
      GOOGLE_PLACE_TYPES.CASINO,
      GOOGLE_PLACE_TYPES.BOWLING_ALLEY,
      GOOGLE_PLACE_TYPES.AMUSEMENT_PARK,
      GOOGLE_PLACE_TYPES.MUSEUM,
      GOOGLE_PLACE_TYPES.ART_GALLERY,
      GOOGLE_PLACE_TYPES.AQUARIUM,
      GOOGLE_PLACE_TYPES.ZOO,
      GOOGLE_PLACE_TYPES.STADIUM,
      GOOGLE_PLACE_TYPES.PARK,
      GOOGLE_PLACE_TYPES.GYM
    ],
    enabled: true,
    searchRadius: 1500,
    maxResults: 10,
    priority: 6,
    color: '#EC4899'
  },
  {
    name: 'finance',
    displayName: 'Finance & Banking',
    description: 'Banks, ATMs, and financial services',
    icon: '🏦',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.BANK,
      GOOGLE_PLACE_TYPES.ATM,
      GOOGLE_PLACE_TYPES.ACCOUNTING,
      GOOGLE_PLACE_TYPES.INSURANCE_AGENCY
    ],
    enabled: true,
    searchRadius: 1500,
    maxResults: 8,
    priority: 5,
    color: '#059669'
  },
  {
    name: 'services',
    displayName: 'Services',
    description: 'Gas stations, post offices, and essential services',
    icon: '⛽',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.BEAUTY_SALON,
      GOOGLE_PLACE_TYPES.HAIR_CARE,
      GOOGLE_PLACE_TYPES.SPA,
      GOOGLE_PLACE_TYPES.LAUNDRY,
      GOOGLE_PLACE_TYPES.CAR_REPAIR,
      GOOGLE_PLACE_TYPES.CAR_WASH,
      GOOGLE_PLACE_TYPES.ELECTRICIAN,
      GOOGLE_PLACE_TYPES.PLUMBER,
      GOOGLE_PLACE_TYPES.LOCKSMITH,
      GOOGLE_PLACE_TYPES.POST_OFFICE
    ],
    enabled: true,
    searchRadius: 1500,
    maxResults: 8,
    priority: 4,
    color: '#6B7280'
  },
  {
    name: 'religious',
    displayName: 'Religious Places',
    description: 'Churches, mosques, temples, and places of worship',
    icon: '⛪',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.CHURCH,
      GOOGLE_PLACE_TYPES.MOSQUE,
      GOOGLE_PLACE_TYPES.SYNAGOGUE,
      GOOGLE_PLACE_TYPES.HINDU_TEMPLE
    ],
    enabled: false, // Disabled by default
    searchRadius: 2000,
    maxResults: 5,
    priority: 3,
    color: '#7C3AED'
  },
  {
    name: 'tourism',
    displayName: 'Tourism & Attractions',
    description: 'Tourist attractions, museums, and points of interest',
    icon: '🗽',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.TOURIST_ATTRACTION,
      GOOGLE_PLACE_TYPES.MUSEUM,
      GOOGLE_PLACE_TYPES.ART_GALLERY,
      GOOGLE_PLACE_TYPES.LODGING,
      GOOGLE_PLACE_TYPES.CAMPGROUND
    ],
    enabled: false, // Disabled by default
    searchRadius: 3000,
    maxResults: 8,
    priority: 2,
    color: '#DC2626'
  }
];

async function populateCategories() {
  try {
    console.log('🔐 Logging in as user...');

    // Try to login with user credentials
    let userToken;
    try {
      const loginResponse = await axios.post(`${API_BASE}/auth/local`, {
        identifier: userCredentials.email,
        password: userCredentials.password
      });
      userToken = loginResponse.data.jwt;
      console.log('✅ User login successful');
    } catch (error) {
      console.log('❌ User login failed. Please check credentials.');
      console.log('Error:', error.response?.data?.error?.message || error.message);
      return;
    }

    console.log('\n📋 Creating place categories...');

    for (const category of defaultCategories) {
      try {
        const response = await axios.post(`${API_BASE}/nearby-place-categories`, {
          data: category
        }, {
          headers: {
            Authorization: `Bearer ${userToken}`,
            'Content-Type': 'application/json'
          }
        });
        
        console.log(`✅ Created category: ${category.displayName} (${category.enabled ? 'enabled' : 'disabled'})`);
      } catch (error) {
        if (error.response?.status === 400 && error.response?.data?.error?.message?.includes('unique')) {
          console.log(`⚠️  Category already exists: ${category.displayName}`);
        } else {
          console.error(`❌ Failed to create category ${category.displayName}:`, error.response?.data?.error?.message || error.message);
        }
      }
    }

    console.log('\n🎉 Place categories setup complete!');
    console.log('\n📋 Summary:');
    console.log(`✅ Total categories: ${defaultCategories.length}`);
    console.log(`✅ Enabled by default: ${defaultCategories.filter(c => c.enabled).length}`);
    console.log(`⚠️  Disabled by default: ${defaultCategories.filter(c => !c.enabled).length}`);
    console.log('\n💡 You can enable/disable categories and adjust settings in the Strapi admin panel.');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
  }
}

// Run the setup
populateCategories();
