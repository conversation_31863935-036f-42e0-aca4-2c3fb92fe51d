// Script to create test data for the real estate application
const axios = require('axios');

const API_BASE = 'http://localhost:1337/api';

// Existing user 'badr' login credentials
const testUser = {
  username: 'badr',
  email: '<EMAIL>',
  password: 'Mb123321'
};

// Additional user profile data to update after registration
const userProfileData = {
  firstName: 'Badr',
  lastName: 'Test User',
  phone: '+1234567890',
  company: 'Test Real Estate',
  jobTitle: 'Property Manager',
  bio: 'Test user for property management',
  isAgent: true
};

// Test properties data for user 'badr'
const testProperties = [
  {
    title: 'Modern Downtown Apartment',
    description: 'Beautiful 2-bedroom apartment in the heart of downtown with stunning city views. Features modern amenities and premium finishes throughout.',
    price: 450000,
    currency: 'USD',
    propertyType: 'apartment',
    status: 'for-sale',
    bedrooms: 2,
    bathrooms: 2,
    area: 1200,
    areaUnit: 'sqft',
    address: '123 Main Street, Downtown',
    city: 'New York',
    country: 'USA',
    neighborhood: 'Downtown',
    features: ['balcony', 'parking', 'gym', 'concierge'],
    yearBuilt: 2020,
    parking: 1,
    furnished: false,
    petFriendly: true,
    featured: true,
    isLuxury: false,
    propertyCode: 'NYC-APT-001'
  },
  {
    title: 'Luxury Waterfront Villa',
    description: 'Stunning 4-bedroom villa with private beach access and panoramic ocean views. Perfect for luxury living with premium amenities.',
    price: 1250000,
    currency: 'USD',
    propertyType: 'villa',
    status: 'for-sale',
    bedrooms: 4,
    bathrooms: 3,
    area: 3500,
    areaUnit: 'sqft',
    address: '456 Ocean Drive, Waterfront',
    city: 'Miami',
    country: 'USA',
    neighborhood: 'Waterfront',
    features: ['pool', 'garden', 'garage', 'security'],
    yearBuilt: 2018,
    parking: 3,
    furnished: true,
    petFriendly: false,
    featured: true,
    isLuxury: true,
    propertyCode: 'MIA-VIL-002'
  },
  {
    title: 'Cozy Family Home',
    description: 'Perfect 3-bedroom family home in a quiet residential neighborhood. Great for families with children.',
    price: 320000,
    currency: 'USD',
    propertyType: 'villa',
    status: 'for-sale',
    bedrooms: 3,
    bathrooms: 2,
    area: 1800,
    areaUnit: 'sqft',
    address: '789 Elm Street, Residential Area',
    city: 'Austin',
    country: 'USA',
    neighborhood: 'Residential Area',
    features: ['garden', 'garage', 'fireplace'],
    yearBuilt: 2015,
    parking: 2,
    furnished: false,
    petFriendly: true,
    featured: false,
    isLuxury: false,
    propertyCode: 'AUS-VIL-003'
  },
  {
    title: 'Historic District Townhouse',
    description: 'Charming 2-bedroom townhouse in the historic district with original features and modern updates.',
    price: 580000,
    currency: 'USD',
    propertyType: 'townhouse',
    status: 'for-sale',
    bedrooms: 2,
    bathrooms: 1,
    area: 1400,
    areaUnit: 'sqft',
    address: '321 Heritage Lane, Historic District',
    city: 'Boston',
    country: 'USA',
    neighborhood: 'Historic District',
    features: ['fireplace', 'hardwood_floors', 'original_features'],
    yearBuilt: 1890,
    parking: 1,
    furnished: false,
    petFriendly: true,
    featured: false,
    isLuxury: false,
    propertyCode: 'BOS-TWN-004'
  },
  {
    title: 'Modern Penthouse Suite',
    description: 'Luxurious penthouse with rooftop terrace and 360-degree city views. The ultimate in urban living.',
    price: 2100000,
    currency: 'USD',
    propertyType: 'penthouse',
    status: 'for-sale',
    bedrooms: 3,
    bathrooms: 3,
    area: 2800,
    areaUnit: 'sqft',
    address: '555 Sky Tower, Uptown',
    city: 'Chicago',
    country: 'USA',
    neighborhood: 'Uptown',
    features: ['terrace', 'elevator', 'concierge', 'gym', 'parking'],
    yearBuilt: 2022,
    parking: 2,
    furnished: true,
    petFriendly: false,
    featured: true,
    isLuxury: true,
    propertyCode: 'CHI-PEN-005'
  }
];

async function createTestData() {
  try {
    console.log('🚀 Starting test data creation...');

    // Step 1: Login with existing user 'badr'
    let userId = null;
    let userToken = null;

    try {
      console.log('� Logging in as existing user "badr"...');

      const loginResponse = await axios.post(`${API_BASE}/auth/local`, {
        identifier: testUser.email,
        password: testUser.password
      });

      userId = loginResponse.data.user.id;
      userToken = loginResponse.data.jwt;

      console.log(`✅ Successfully logged in as user "badr" with ID: ${userId}`);
      console.log(`👤 User details: ${loginResponse.data.user.username} (${loginResponse.data.user.email})`);

    } catch (error) {
      console.error('❌ Failed to login as user "badr":', error.response?.data || error.message);
      return;
    }

    // Step 2: Assign membership to user
    try {
      console.log('🎯 Assigning Premium membership to user...');

      const membershipResponse = await axios.post(`${API_BASE}/memberships/subscribe`, {
        membershipId: 3 // Premium membership (ID 3)
      }, {
        headers: {
          Authorization: `Bearer ${userToken}`
        }
      });

      console.log('✅ Premium membership assigned successfully');
    } catch (error) {
      console.log('⚠️ Could not assign membership (user may already have one):', error.response?.data?.error?.message || error.message);
    }
    
    // Step 3: Create properties for user 'badr'
    console.log('🏠 Creating test properties...');

    for (let i = 0; i < testProperties.length; i++) {
      const property = testProperties[i];

      try {
        // Try creating property with explicit owner relation
        const propertyData = {
          ...property,
          owner: {
            id: userId
          }
        };

        console.log(`Creating property: ${property.title}`);
        console.log('Property data:', JSON.stringify(propertyData, null, 2));

        const propertyResponse = await axios.post(`${API_BASE}/properties`, {
          data: propertyData
        }, {
          headers: {
            Authorization: `Bearer ${userToken}`,
            'Content-Type': 'application/json'
          }
        });

        console.log(`✅ Created property: ${property.title} (ID: ${propertyResponse.data.data.id})`);
      } catch (error) {
        console.error(`❌ Failed to create property: ${property.title}`);
        console.error('Status:', error.response?.status);
        console.error('Error details:', JSON.stringify(error.response?.data, null, 2));
        console.error('Full error:', error.message);

        // If it's a validation error, let's see the details
        if (error.response?.status === 400) {
          console.error('Validation error details:', error.response.data);
        }
      }
    }

    console.log('🎉 Test data creation completed successfully!');
    
  } catch (error) {
    console.error('❌ Error creating test data:', error.message);
  }
}

// Run the script
createTestData();
