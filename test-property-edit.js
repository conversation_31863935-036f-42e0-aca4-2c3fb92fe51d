const fetch = require('node-fetch');

async function testPropertyEdit() {
  const STRAPI_URL = 'http://localhost:1337';
  
  try {
    // Login first
    console.log('🔐 Logging in...');
    const loginResponse = await fetch(`${STRAPI_URL}/api/auth/local`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        identifier: '<EMAIL>',
        password: 'Mb123321'
      })
    });
    
    if (!loginResponse.ok) {
      throw new Error('Login failed');
    }
    
    const loginData = await loginResponse.json();
    const jwt = loginData.jwt;
    console.log('✅ Login successful');
    
    // Get user's properties
    console.log('📋 Getting user properties...');
    const myPropsResponse = await fetch(`${STRAPI_URL}/api/properties/my-properties`, {
      headers: { 'Authorization': `Bearer ${jwt}` }
    });
    
    if (!myPropsResponse.ok) {
      throw new Error('Failed to get properties');
    }
    
    const myPropsData = await myPropsResponse.json();
    console.log(`Found ${myPropsData.data.length} properties`);
    
    if (myPropsData.data.length === 0) {
      console.log('❌ No properties found to test edit');
      return;
    }
    
    const testProperty = myPropsData.data[0];
    console.log(`Testing with property: ${testProperty.title} (ID: ${testProperty.id}, DocumentId: ${testProperty.documentId})`);
    
    // Test getForEdit with documentId
    console.log('🔍 Testing getForEdit with documentId...');
    const editResponse = await fetch(`${STRAPI_URL}/api/properties/${testProperty.documentId}/edit`, {
      headers: { 'Authorization': `Bearer ${jwt}` }
    });
    
    if (editResponse.ok) {
      const editData = await editResponse.json();
      console.log('✅ getForEdit with documentId successful');
      console.log(`   Property title: ${editData.data.title}`);
    } else {
      console.log(`❌ getForEdit with documentId failed: ${editResponse.status}`);
      
      // Try with numeric ID
      console.log('🔍 Testing getForEdit with numeric ID...');
      const editResponse2 = await fetch(`${STRAPI_URL}/api/properties/${testProperty.id}/edit`, {
        headers: { 'Authorization': `Bearer ${jwt}` }
      });
      
      if (editResponse2.ok) {
        const editData2 = await editResponse2.json();
        console.log('✅ getForEdit with numeric ID successful');
        console.log(`   Property title: ${editData2.data.title}`);
      } else {
        console.log(`❌ getForEdit with numeric ID also failed: ${editResponse2.status}`);
      }
    }
    
    // Test property update with documentId
    console.log('✏️ Testing property update with documentId...');
    const updateData = {
      data: {
        title: testProperty.title + ' - Updated Test',
        description: 'Updated description for testing'
      }
    };
    
    const updateResponse = await fetch(`${STRAPI_URL}/api/properties/${testProperty.documentId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwt}`
      },
      body: JSON.stringify(updateData)
    });
    
    if (updateResponse.ok) {
      const updateResult = await updateResponse.json();
      console.log('✅ Property update with documentId successful');
      console.log(`   Updated title: ${updateResult.data?.title || 'No title in response'}`);
      console.log('   Full response:', JSON.stringify(updateResult, null, 2));
    } else {
      console.log(`❌ Property update with documentId failed: ${updateResponse.status}`);
      const errorData = await updateResponse.json();
      console.log('   Error:', errorData.error?.message || 'Unknown error');

      // Try with numeric ID
      console.log('✏️ Testing property update with numeric ID...');
      const updateResponse2 = await fetch(`${STRAPI_URL}/api/properties/${testProperty.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${jwt}`
        },
        body: JSON.stringify(updateData)
      });

      if (updateResponse2.ok) {
        const updateResult2 = await updateResponse2.json();
        console.log('✅ Property update with numeric ID successful');
        console.log(`   Updated title: ${updateResult2.data?.title || 'No title in response'}`);
        console.log('   Response structure:', Object.keys(updateResult2));
      } else {
        console.log(`❌ Property update with numeric ID also failed: ${updateResponse2.status}`);
        const errorData2 = await updateResponse2.json();
        console.log('   Error:', errorData2.error?.message || 'Unknown error');
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testPropertyEdit();
