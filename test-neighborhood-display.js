// Test script to check neighborhood display in My Properties
const axios = require('axios');

const API_BASE = 'http://localhost:1337/api';

// Existing user 'badr' login credentials
const testUser = {
  email: '<EMAIL>',
  password: 'Mb123321'
};

async function testNeighborhoodDisplay() {
  try {
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/local`, {
      identifier: testUser.email,
      password: testUser.password
    });
    
    const userId = loginResponse.data.user.id;
    const userToken = loginResponse.data.jwt;
    console.log(`✅ Logged in as user ID: ${userId}`);
    
    // Get user's properties to check neighborhood format
    console.log('\n📋 Fetching user properties...');
    const propertiesResponse = await axios.get(`${API_BASE}/properties/my-properties`, {
      headers: {
        Authorization: `Bearer ${userToken}`
      }
    });
    
    const properties = propertiesResponse.data.data;
    console.log(`✅ Found ${properties.length} properties`);
    
    // Check neighborhood data format for each property
    properties.forEach((property, index) => {
      console.log(`\n🏠 Property ${index + 1}: ${property.title}`);
      console.log(`   ID: ${property.id}`);
      console.log(`   Neighborhood type: ${typeof property.neighborhood}`);
      console.log(`   Neighborhood value:`, property.neighborhood);
      
      if (Array.isArray(property.neighborhood)) {
        console.log(`   ✅ Neighborhood is array with ${property.neighborhood.length} items`);
        property.neighborhood.forEach((n, i) => {
          console.log(`      ${i + 1}. ${typeof n === 'object' ? n.name : n}`);
        });
      } else if (typeof property.neighborhood === 'string') {
        console.log(`   ⚠️  Neighborhood is string: "${property.neighborhood}"`);
      } else if (property.neighborhood === null || property.neighborhood === undefined) {
        console.log(`   ⚠️  Neighborhood is null/undefined`);
      } else {
        console.log(`   ❓ Neighborhood is unknown type`);
      }
    });
    
    // Test creating a property with mixed neighborhood formats to see what happens
    console.log('\n🧪 Testing mixed neighborhood formats...');
    
    // Test 1: Old string format
    console.log('\n1. Testing old string format...');
    try {
      const oldFormatProperty = {
        title: 'Test Old Format Neighborhood',
        description: 'Testing old string neighborhood format',
        price: 100000,
        propertyType: 'apartment',
        area: 1000,
        address: '123 Old Format Street',
        city: 'Test City',
        country: 'USA',
        neighborhood: 'Downtown' // Old string format
      };
      
      const response1 = await axios.post(`${API_BASE}/properties`, {
        data: oldFormatProperty
      }, {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('   ✅ Old format accepted');
      console.log('   Stored as:', typeof response1.data.data.neighborhood, response1.data.data.neighborhood);
    } catch (error) {
      console.log('   ❌ Old format failed:', error.response?.data?.error?.message || error.message);
    }
    
    // Test 2: New array format
    console.log('\n2. Testing new array format...');
    try {
      const newFormatProperty = {
        title: 'Test New Format Neighborhood',
        description: 'Testing new array neighborhood format',
        price: 150000,
        propertyType: 'villa',
        area: 1500,
        address: '456 New Format Avenue',
        city: 'Test City',
        country: 'USA',
        neighborhood: [
          {
            name: 'Business District',
            type: 'neighborhood',
            formatted_address: 'Business District, Test City, USA'
          },
          {
            name: 'Financial Center',
            type: 'sublocality',
            formatted_address: 'Financial Center, Test City, USA'
          }
        ]
      };
      
      const response2 = await axios.post(`${API_BASE}/properties`, {
        data: newFormatProperty
      }, {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('   ✅ New format accepted');
      console.log('   Stored as:', typeof response2.data.data.neighborhood);
      console.log('   Data:', JSON.stringify(response2.data.data.neighborhood, null, 2));
    } catch (error) {
      console.log('   ❌ New format failed:', error.response?.data?.error?.message || error.message);
    }
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
  }
}

// Run the test
testNeighborhoodDisplay();
