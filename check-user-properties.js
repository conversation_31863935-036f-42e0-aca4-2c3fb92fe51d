// Script to check user's current properties and membership limits
const axios = require('axios');

const API_BASE = 'http://localhost:1337/api';

// Existing user 'badr' login credentials
const testUser = {
  email: '<EMAIL>',
  password: 'Mb123321'
};

async function checkUserProperties() {
  try {
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/local`, {
      identifier: testUser.email,
      password: testUser.password
    });
    
    const userId = loginResponse.data.user.id;
    const userToken = loginResponse.data.jwt;
    console.log(`✅ Logged in as user ID: ${userId}`);
    
    // Get user's properties
    console.log('🏠 Fetching user properties...');
    try {
      const propertiesResponse = await axios.get(`${API_BASE}/properties/my-properties`, {
        headers: {
          Authorization: `Bearer ${userToken}`
        }
      });
      
      const properties = propertiesResponse.data.data;
      console.log(`📊 User has ${properties.length} properties:`);
      
      properties.forEach((property, index) => {
        console.log(`  ${index + 1}. ${property.title} (ID: ${property.id})`);
      });
      
    } catch (error) {
      console.error('❌ Failed to fetch properties:', error.response?.data || error.message);
    }
    
    // Get user's membership details
    console.log('\n👤 Fetching user membership details...');
    try {
      const userResponse = await axios.get(`${API_BASE}/users/me?populate=membership`, {
        headers: {
          Authorization: `Bearer ${userToken}`
        }
      });
      
      const user = userResponse.data;
      console.log('User membership:', user.membership);
      
      if (user.membership) {
        console.log(`📋 Membership: ${user.membership.name}`);
        console.log(`🏠 Max Properties: ${user.membership.maxProperties}`);
        console.log(`🖼️ Max Images: ${user.membership.maxImages}`);
        console.log(`🏗️ Can Create Projects: ${user.membership.canCreateProjects}`);
      } else {
        console.log('❌ No membership found');
      }
      
    } catch (error) {
      console.error('❌ Failed to fetch user details:', error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
  }
}

// Run the check
checkUserProperties();
