"use strict";
/**
 * notification service
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreService('api::notification.notification', ({ strapi }) => ({
    /**
     * Create a notification for a user
     */
    async createNotification({ recipientId, title, message, type = 'info', priority = 'normal', senderId = null, relatedPropertyId = null, relatedProjectId = null, relatedMessageId = null, actionUrl = null, actionText = null, metadata = null, expiresAt = null }) {
        try {
            const notification = await strapi.entityService.create('api::notification.notification', {
                data: {
                    title,
                    message,
                    type,
                    priority,
                    recipient: recipientId,
                    sender: senderId,
                    relatedProperty: relatedPropertyId,
                    relatedProject: relatedProjectId,
                    relatedMessage: relatedMessageId,
                    actionUrl,
                    actionText,
                    metadata,
                    expiresAt,
                    isRead: false
                },
                populate: ['recipient', 'sender', 'relatedProperty', 'relatedProject', 'relatedMessage']
            });
            return notification;
        }
        catch (error) {
            strapi.log.error('Error creating notification:', error);
            throw error;
        }
    },
    /**
     * Create property-related notifications
     */
    async createPropertyNotification({ recipientId, propertyId, type, senderId = null, actionUrl = null }) {
        const property = await strapi.entityService.findOne('api::property.property', propertyId, {
            populate: ['owner']
        });
        if (!property) {
            throw new Error('Property not found');
        }
        let title, message, actionText;
        switch (type) {
            case 'property_inquiry':
                title = 'New Property Inquiry';
                message = `Someone is interested in your property "${property.title}"`;
                actionText = 'View Inquiry';
                break;
            case 'property_approved':
                title = 'Property Approved';
                message = `Your property "${property.title}" has been approved and is now live`;
                actionText = 'View Property';
                break;
            case 'property_rejected':
                title = 'Property Needs Review';
                message = `Your property "${property.title}" needs some updates before it can be published`;
                actionText = 'Edit Property';
                break;
            default:
                title = 'Property Update';
                message = `Update regarding your property "${property.title}"`;
                actionText = 'View Property';
        }
        return this.createNotification({
            recipientId,
            title,
            message,
            type,
            senderId,
            relatedPropertyId: propertyId,
            actionUrl: actionUrl || `/properties/${propertyId}`,
            actionText,
            priority: type === 'property_inquiry' ? 'high' : 'normal'
        });
    },
    /**
     * Create message-related notifications
     */
    async createMessageNotification({ recipientId, messageId, senderId, actionUrl = null }) {
        var _a, _b, _c, _d;
        const message = await strapi.entityService.findOne('api::message.message', messageId, {
            populate: ['sender', 'property', 'project']
        });
        if (!message) {
            throw new Error('Message not found');
        }
        const senderName = ((_a = message.sender) === null || _a === void 0 ? void 0 : _a.firstName) || ((_b = message.sender) === null || _b === void 0 ? void 0 : _b.username) || 'Someone';
        let title = 'New Message';
        let notificationMessage = `${senderName} sent you a message`;
        if (message.property) {
            notificationMessage += ` about "${message.property.title}"`;
        }
        else if (message.project) {
            notificationMessage += ` about "${message.project.title}"`;
        }
        return this.createNotification({
            recipientId,
            title,
            message: notificationMessage,
            type: 'message_received',
            senderId,
            relatedMessageId: messageId,
            relatedPropertyId: (_c = message.property) === null || _c === void 0 ? void 0 : _c.id,
            relatedProjectId: (_d = message.project) === null || _d === void 0 ? void 0 : _d.id,
            actionUrl: actionUrl || `/dashboard/messages/${messageId}`,
            actionText: 'View Message',
            priority: 'normal'
        });
    },
    /**
     * Create system notifications
     */
    async createSystemNotification({ recipientId, title, message, priority = 'normal', actionUrl = null, actionText = null, expiresAt = null }) {
        return this.createNotification({
            recipientId,
            title,
            message,
            type: 'system',
            priority,
            actionUrl,
            actionText,
            expiresAt
        });
    },
    /**
     * Clean up expired notifications
     */
    async cleanupExpiredNotifications() {
        try {
            const expiredNotifications = await strapi.db.query('api::notification.notification').findMany({
                where: {
                    expiresAt: {
                        $lt: new Date()
                    }
                }
            });
            if (expiredNotifications.length > 0) {
                await strapi.db.query('api::notification.notification').deleteMany({
                    where: {
                        id: {
                            $in: expiredNotifications.map(n => n.id)
                        }
                    }
                });
                strapi.log.info(`Cleaned up ${expiredNotifications.length} expired notifications`);
            }
        }
        catch (error) {
            strapi.log.error('Error cleaning up expired notifications:', error);
        }
    }
}));
