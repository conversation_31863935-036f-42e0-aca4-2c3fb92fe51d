{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/zh.json.mjs"], "sourcesContent": ["var configurations = \"配置設定\";\nvar from = \"從\";\nvar zh = {\n    \"attribute.boolean\": \"布林值\",\n    \"attribute.boolean.description\": \"好或不好、1 或 0、是或否\",\n    \"attribute.component\": \"元件\",\n    \"attribute.component.description\": \"組合欄位值以便重複使用\",\n    \"attribute.customField\": \"自訂欄位\",\n    \"attribute.date\": \"日期\",\n    \"attribute.date.description\": \"時間選擇器，可選擇日期及時間\",\n    \"attribute.datetime\": \"日期和時間\",\n    \"attribute.dynamiczone\": \"動態配置\",\n    \"attribute.dynamiczone.description\": \"動態選擇元件及編輯其內容\",\n    \"attribute.email\": \"電子郵件地址\",\n    \"attribute.email.description\": \"驗證格式過的電子郵件欄位\",\n    \"attribute.enumeration\": \"列舉\",\n    \"attribute.enumeration.description\": \"列舉設定值並可擇一為值\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.json.description\": \"JSON 資料格式\",\n    \"attribute.media\": \"媒體\",\n    \"attribute.media.description\": \"圖片、影片等檔案\",\n    \"attribute.null\": \" \",\n    \"attribute.number\": \"數字\",\n    \"attribute.number.description\": \"數字 (正整數、浮點數、小數)\",\n    \"attribute.password\": \"密碼\",\n    \"attribute.password.description\": \"加密過的密碼欄位\",\n    \"attribute.relation\": \"關聯\",\n    \"attribute.relation.description\": \"指向其他集合類型\",\n    \"attribute.richtext\": \"多文本格式\",\n    \"attribute.richtext.description\": \"多文本格式編輯器\",\n    \"attribute.text\": \"文字\",\n    \"attribute.text.description\": \"短的標題或長的描述文字\",\n    \"attribute.time\": \"時間\",\n    \"attribute.timestamp\": \"時間戳記\",\n    \"attribute.uid\": \"UID\",\n    \"attribute.uid.description\": \"唯一識別碼\",\n    \"button.attributes.add.another\": \"新增欄位\",\n    \"button.component.add\": \"新增元件\",\n    \"button.component.create\": \"建立新元件\",\n    \"button.model.create\": \"建立新的集合類型\",\n    \"button.single-types.create\": \"建立新的單一類型\",\n    \"component.repeatable\": \"(可重複的)\",\n    \"components.SelectComponents.displayed-value\": \" 已選擇 {number, plural, =0 {# components} 一個 {# component} 其他 {# components}}\",\n    \"components.componentSelect.no-component-available\": \"你已經加入了所有可以加入的元件\",\n    \"components.componentSelect.no-component-available.with-search\": \"搜尋不到符合的元件\",\n    \"components.componentSelect.value-component\": \"{number} 個元件被選取 (輸入搜尋元件)\",\n    \"components.componentSelect.value-components\": \"{number} 個元件被選取\",\n    configurations: configurations,\n    \"contentType.apiId-plural.description\": \"複數 API ID\",\n    \"contentType.apiId-plural.label\": \"API ID (複數)\",\n    \"contentType.apiId-singular.description\": \"UID 用於產生 API 路徑和資料庫資料表/集合的 UID\",\n    \"contentType.apiId-singular.label\": \"API ID (單數)\",\n    \"contentType.collectionName.description\": \"使用於當內容集合名稱與資料集合名稱不同時\",\n    \"contentType.collectionName.label\": \"集合名稱\",\n    \"contentType.displayName.label\": \"顯示名稱\",\n    \"contentType.kind.change.warning\": \"你變更了內容類型：API 將會被重置 (路由器、控制器、和服務將會被覆寫)。\",\n    \"error.attributeName.reserved-name\": \"這個欄位名稱不可以在此內容類型內被使用，有可能會破壞其他功能\",\n    \"error.contentType.pluralName-used\": \"此數值不能與單數相同\",\n    \"error.contentType.singularName-used\": \"此數值不能與複數相同\",\n    \"error.contentTypeName.reserved-name\": \"這個內容類型名稱不可以在此專案內被使用，有可能會破壞其他功能\",\n    \"error.validation.enum-duplicate\": \"不允取重複的值\",\n    \"error.validation.enum-empty-string\": \"不允許空字串\",\n    \"error.validation.enum-regex\": \"有一個以上的數值無效。數值在首個數字前應先有英文字母。\",\n    \"error.validation.minSupMax\": \"Can't be superior\",\n    \"error.validation.positive\": \"必須為正數\",\n    \"error.validation.regex\": \"不合法的正規式\",\n    \"error.validation.relation.targetAttribute-taken\": \"該欄位名稱已被使用\",\n    \"form.attribute.component.option.add\": \"新增元件\",\n    \"form.attribute.component.option.create\": \"建立新元件\",\n    \"form.attribute.component.option.create.description\": \"一個可以跨元件及類型引用的元件, 可以在任何地方被引用\",\n    \"form.attribute.component.option.repeatable\": \"可重複的元件\",\n    \"form.attribute.component.option.repeatable.description\": \"適用於陣列，清單，標籤類型元件\",\n    \"form.attribute.component.option.reuse-existing\": \"使用一個已建立的元件\",\n    \"form.attribute.component.option.reuse-existing.description\": \"可複用元件已建立，可以協助你在不同的內容型別間保持資料一致性\",\n    \"form.attribute.component.option.single\": \"單一元件\",\n    \"form.attribute.component.option.single.description\": \"適用於組合型欄位群，如：完整地址,基本完整資訊 ......等\",\n    \"form.attribute.item.customColumnName\": \"自訂欄位名稱\",\n    \"form.attribute.item.customColumnName.description\": \"將資料庫欄位名稱以更易懂的格式重新命名，對 API 回應很有用。\",\n    \"form.attribute.item.date.type.date\": \"date (例如：01/01/{currentYear})\",\n    \"form.attribute.item.date.type.datetime\": \"datetime (例如：01/01/{currentYear} 00:00 AM)\",\n    \"form.attribute.item.date.type.time\": \"time (例如：00:00 AM)\",\n    \"form.attribute.item.defineRelation.fieldName\": \"欄位名稱\",\n    \"form.attribute.item.enumeration.graphql\": \"GraphQL 名稱覆寫\",\n    \"form.attribute.item.enumeration.graphql.description\": \"可以讓您覆寫 GraphQL 的預設名稱\",\n    \"form.attribute.item.enumeration.placeholder\": \"例:\\n早上\\n中午\\n晚上\",\n    \"form.attribute.item.enumeration.rules\": \"值 (格式為一行一個值)\",\n    \"form.attribute.item.maximum\": \"最大數值\",\n    \"form.attribute.item.maximumLength\": \"最大長度\",\n    \"form.attribute.item.minimum\": \"最小數值\",\n    \"form.attribute.item.minimumLength\": \"最小長度\",\n    \"form.attribute.item.number.type\": \"數字格式\",\n    \"form.attribute.item.number.type.biginteger\": \"大整數 (例如：123456789)\",\n    \"form.attribute.item.number.type.decimal\": \"浮點數 (decimal) (例: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"浮點數 (float) (例: 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"整數 (例: 10)\",\n    \"form.attribute.item.privateField\": \"隱密欄位\",\n    \"form.attribute.item.privateField.description\": \"該欄位不會被顯示在 API 的回傳資料中\",\n    \"form.attribute.item.requiredField\": \"必填欄位\",\n    \"form.attribute.item.requiredField.description\": \"如果這個欄位留空，您將不能建立項目。\",\n    \"form.attribute.item.text.regex\": \"正規式\",\n    \"form.attribute.item.text.regex.description\": \"文本正規表達式\",\n    \"form.attribute.item.uniqueField\": \"唯一欄位\",\n    \"form.attribute.item.uniqueField.description\": \"如果已存在的項目有一模一樣的內容，您將不能建立項目。\",\n    \"form.attribute.media.allowed-types\": \"選擇合法的多媒體類型\",\n    \"form.attribute.media.allowed-types.option-files\": \"檔案\",\n    \"form.attribute.media.allowed-types.option-images\": \"圖片\",\n    \"form.attribute.media.allowed-types.option-videos\": \"影片\",\n    \"form.attribute.media.option.multiple\": \"複數多媒體\",\n    \"form.attribute.media.option.multiple.description\": \"適用於輪撥模組或多檔案下載\",\n    \"form.attribute.media.option.single\": \"單一多媒體\",\n    \"form.attribute.media.option.single.description\": \"適用於大頭貼或背景圖\",\n    \"form.attribute.settings.default\": \"預設值\",\n    \"form.attribute.text.option.long-text\": \"長文字\",\n    \"form.attribute.text.option.long-text.description\": \"適用於描述及自傳內容，但無法被精準搜尋\",\n    \"form.attribute.text.option.short-text\": \"短文字\",\n    \"form.attribute.text.option.short-text.description\": \"適用於標題,名稱,連結且可以被精準搜尋\",\n    \"form.button.add-components-to-dynamiczone\": \"新增元件至動態配置\",\n    \"form.button.add-field\": \"新增欄位\",\n    \"form.button.add-first-field-to-created-component\": \"新增元件的第一個欄位\",\n    \"form.button.add.field.to.collectionType\": \"新增其他欄位於此集合類型\",\n    \"form.button.add.field.to.component\": \"新增其他欄位於此元件\",\n    \"form.button.add.field.to.contentType\": \"新增其他欄位於此內容類型\",\n    \"form.button.add.field.to.singleType\": \"新增其他欄位於此單一類型\",\n    \"form.button.cancel\": \"取消\",\n    \"form.button.collection-type.description\": \"適用於複數實例，如議題文章、產品、貼文......等\",\n    \"form.button.collection-type.name\": \"集合型別\",\n    \"form.button.configure-component\": \"配置元件\",\n    \"form.button.configure-view\": \"配置畫面\",\n    \"form.button.select-component\": \"選擇一個元件\",\n    \"form.button.single-type.description\": \"適用於單一實例，如首頁、關於 ...... 等\",\n    \"form.button.single-type.name\": \"單一型別\",\n    from: from,\n    \"menu.section.components.name\": \"元件\",\n    \"menu.section.models.name\": \"集合型別\",\n    \"menu.section.single-types.name\": \"單一型別\",\n    \"modalForm.attribute.form.base.name.description\": \"欄位名稱不允許空白\",\n    \"modalForm.attribute.form.base.name.placeholder\": \"例如：slug、seo網址、canonical網址\",\n    \"modalForm.attribute.target-field\": \"關聯目標欄位\",\n    \"modalForm.attributes.select-component\": \"選擇一個元件\",\n    \"modalForm.attributes.select-components\": \"選擇多個元件\",\n    \"modalForm.collectionType.header-create\": \"建立集合型別\",\n    \"modalForm.component.header-create\": \"建立元件\",\n    \"modalForm.components.create-component.category.label\": \"選擇或新增一個分類\",\n    \"modalForm.components.icon.label\": \"圖示\",\n    \"modalForm.empty.button\": \"新增自訂欄位\",\n    \"modalForm.empty.heading\": \"這裡目前什麼都沒有。\",\n    \"modalForm.empty.sub-heading\": \"透過擴充功能找到您想搜尋的項目。\",\n    \"modalForm.editCategory.base.name.description\": \"分類名稱不允許空白\",\n    \"modalForm.header-edit\": \"編輯 {name}\",\n    \"modalForm.header.categories\": \"複數分類\",\n    \"modalForm.header.back\": \"後退\",\n    \"modalForm.singleType.header-create\": \"建立單一型別\",\n    \"modalForm.sub-header.addComponentToDynamicZone\": \"在動態配置區新增一個元件\",\n    \"modalForm.sub-header.attribute.create\": \"新增 {type} 欄位\",\n    \"modalForm.sub-header.attribute.create.step\": \"新增元件 ({step}/2)\",\n    \"modalForm.sub-header.attribute.edit\": \"編輯 {name}\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"選擇集合型別的一個欄位\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"選擇元件的一個欄位\",\n    \"modalForm.sub-header.chooseAttribute.singleType\": \"選擇單一型別的一個欄位\",\n    \"modalForm.custom-fields.advanced.settings.extended\": \"擴充設定\",\n    \"modalForm.tabs.custom\": \"自訂\",\n    \"modalForm.tabs.custom.howToLink\": \"如何新增自訂欄位\",\n    \"modalForm.tabs.default\": \"預設\",\n    \"modalForm.tabs.label\": \"預設和自訂類型分頁\",\n    \"modelPage.attribute.relation-polymorphic\": \"關聯 (多對多多型)\",\n    \"modelPage.attribute.relationWith\": \"關聯到\",\n    \"notification.error.dynamiczone-min.validation\": \"至少要有一個位於動態區域中的元件才能儲存內容型別\",\n    \"notification.info.autoreaload-disable\": \"啟用這個擴充模組需要重新啟動，使用 `strapi develop` 指令重新啟動\",\n    \"notification.info.creating.notSaved\": \"在建立新的集合型別或元件前請記得儲存\",\n    \"plugin.description.long\": \"為您的 API 定義資料結構，使您輕鬆新增欄位和關聯結構，您所做的修改會自動更新專案。\",\n    \"plugin.description.short\": \"為您的 API 定義資料結構\",\n    \"plugin.name\": \"內容型別建立者\",\n    \"popUpForm.navContainer.advanced\": \"進階設定\",\n    \"popUpForm.navContainer.base\": \"基本設定\",\n    \"popUpWarning.bodyMessage.cancel-modifications\": \"你確定要取消你的修改嗎？\",\n    \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"你確定要取消你的修改嗎？部份元件已被新增或修改...\",\n    \"popUpWarning.bodyMessage.category.delete\": \"你確定要刪除此分類嗎？所有此分類內的元件都會被刪除.\",\n    \"popUpWarning.bodyMessage.component.delete\": \"你確定要刪除此元件嗎？\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"您確定要刪除這個資料結構嗎？\",\n    \"popUpWarning.draft-publish.button.confirm\": \"是的，請停用\",\n    \"popUpWarning.draft-publish.message\": \"如果您停用了草稿/發佈系統，您的草稿將被刪除。\",\n    \"popUpWarning.draft-publish.second-message\": \"您確定要停用嗎？\",\n    \"prompt.unsaved\": \"你確定要離開嗎？所有你的修改都會失效。\",\n    \"relation.attributeName.placeholder\": \"例如：作者、類別、標籤...\",\n    \"relation.manyToMany\": \"有而且屬於許多\",\n    \"relation.manyToOne\": \"有許多\",\n    \"relation.manyWay\": \"有許多\",\n    \"relation.oneToMany\": \"屬於許多\",\n    \"relation.oneToOne\": \"一對一到\",\n    \"relation.oneWay\": \"有一個\",\n    \"table.button.no-fields\": \"新增欄位\",\n    \"table.content.create-first-content-type\": \"建立您的首個集合型別\",\n    \"table.content.no-fields.collection-type\": \"向此集合型別新增首個欄位\",\n    \"table.content.no-fields.component\": \"向此元件新增首個欄位\"\n};\n\nexport { configurations, zh as default, from };\n//# sourceMappingURL=zh.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,wCAAwC;AAAA,EACxC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC;AAAA,EACA,gCAAgC;AAAA,EAChC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,yBAAyB;AAAA,EACzB,mCAAmC;AAAA,EACnC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,qCAAqC;AACzC;", "names": []}