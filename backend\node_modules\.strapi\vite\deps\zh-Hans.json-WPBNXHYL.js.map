{"version": 3, "sources": ["../../../@strapi/upload/dist/admin/translations/zh-Hans.json.mjs"], "sourcesContent": ["var zhHans = {\n    \"header.actions.add-assets\": \"添加新素材\",\n    \"header.actions.add-folder\": \"添加新文件夹\",\n    \"button.next\": \"下一步\",\n    \"checkControl.crop-duplicate\": \"复制裁剪素材\",\n    \"checkControl.crop-original\": \"裁剪原始素材\",\n    \"control-card.add\": \"添加\",\n    \"control-card.cancel\": \"取消\",\n    \"control-card.copy-link\": \"复制链接\",\n    \"control-card.crop\": \"裁剪\",\n    \"control-card.download\": \"下载\",\n    \"control-card.edit\": \"编辑\",\n    \"control-card.replace-media\": \"替换媒体文件\",\n    \"control-card.save\": \"保存\",\n    \"filter.add\": \"添加筛选条件\",\n    \"form.button.replace-media\": \"替换媒体\",\n    \"form.input.description.file-alt\": \"当此素材无法显示时，会显示该文本。\",\n    \"form.input.label.file-alt\": \"备用文本\",\n    \"form.input.label.file-caption\": \"标题\",\n    \"form.input.label.file-name\": \"文件名\",\n    \"form.input.label.file-location\": \"位置\",\n    \"form.input.label.folder-name\": \"文件夹名称\",\n    \"form.input.label.folder-location\": \"位置\",\n    \"form.input.label.folder-location-default-label\": \"媒体库\",\n    \"form.upload-url.error.url.invalid\": \"有一个链接格式不合法\",\n    \"form.upload-url.error.url.invalids\": \"{number}个链接格式不合法\",\n    \"header.actions.upload-assets\": \"上传素材\",\n    \"header.content.assets\": \"{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 个素材} other {# 个素材}}\",\n    \"input.button.label\": \"浏览文件\",\n    \"input.label-bold\": \"拖拽至此\",\n    \"input.label-normal\": \"并上传\",\n    \"input.placeholder\": \"点击选择一个素材，或者拖拽文件至区域\",\n    \"input.placeholder.icon\": \"在此区域内放置素材\",\n    \"input.url.description\": \"回车键分隔URL链接\",\n    \"input.url.label\": \"URL\",\n    \"list.assets-empty.subtitle\": \"添加一个到列表中\",\n    \"list.assets-empty.title\": \"暂无素材\",\n    \"list.assets-empty.title-withSearch\": \"无素材符合当前筛选条件\",\n    \"list.assets.empty\": \"上传你的第一个素材\",\n    \"list.assets.loading-asset\": \"正在为媒体加载预览: {path}\",\n    \"list.assets.not-supported-content\": \"无可用预览\",\n    \"list.assets.preview-asset\": \"预览视频（路径：{path}）\",\n    \"list.assets.selected\": \"{number, plural, one {# 个素材} other {# 个素材}}准备上传\",\n    \"list.assets.type-not-allowed\": \"文件类型不合法\",\n    \"list.table.header.actions\": \"操作\",\n    \"list.table.header.preview\": \"预览\",\n    \"list.table.header.name\": \"名称\",\n    \"list.table.header.ext\": \"扩展名\",\n    \"list.table.header.size\": \"大小\",\n    \"list.table.header.createdAt\": \"创建时间\",\n    \"list.table.header.updatedAt\": \"最后更新时间\",\n    \"list.table.header.sort\": \"按{label}排序\",\n    \"list.table.content.empty-label\": \"此字段为空\",\n    \"list.assets.title\": \"资源({count})\",\n    \"list.asset.at.finished\": \"资源已经加载完毕\",\n    \"list.assets-empty.search\": \"未找到结果\",\n    \"list.assets.empty-upload\": \"上传你的第一个资源...\",\n    \"list.assets.empty.no-permissions\": \"没有查看权限\",\n    \"list-assets-select\": \"选择{name}资源\",\n    \"list.assets.to-upload\": \"有{number, plural, =0 {没有资源} one {1个资源} other {#个资产}}可以上传\",\n    \"list.folder.edit\": \"编辑文件夹\",\n    \"list.folder.select\": \"选择{name}文件夹\",\n    \"list.folder.subtitle\": \"{folderCount, plural, =0 {#个文件夹} one {#个文件夹} other {#个文件夹}}, {filesCount, plural, =0 {#个资源} one {#个资源} other {#个资源}}\",\n    \"list.folders.title\": \"文件夹（{count}）\",\n    \"list.folders.link-label\": \"访问文件夹\",\n    \"mediaLibraryInput.actions.nextSlide\": \"下一张幻灯片\",\n    \"mediaLibraryInput.actions.previousSlide\": \"上一张幻灯片\",\n    \"mediaLibraryInput.placeholder\": \"点击添加素材，或拖放一个素材到当前区域\",\n    \"mediaLibraryInput.slideCount\": \"共 {m} 张，第 {n} 张\",\n    \"modal.file-details.date\": \"日期\",\n    \"modal.file-details.dimensions\": \"尺寸\",\n    \"modal.file-details.extension\": \"扩展名\",\n    \"modal.file-details.size\": \"大小\",\n    \"modal.file-details.id\": \"资源ID\",\n    \"modal.folder.elements.count\": \"{folderCount}个文件夹，{assetCount}个资源\",\n    \"modal.folder.move.title\": \"将元素移动到\",\n    \"modal.remove.success-label\": \"元素已成功删除\",\n    \"modal.move.success-label\": \"元素已成功移动\",\n    \"modal.upload.cancelled\": \"手动上传已中止\",\n    \"modal.folder.create.title\": \"新增文件夹\",\n    \"modal.folder.create.submit\": \"新增\",\n    \"modal.folder.cancel\": \"取消\",\n    \"modal.header.go-back\": \"返回\",\n    \"modal.header.browse\": \"上传素材\",\n    \"modal.header.file-detail\": \"详情\",\n    \"modal.header.pending-assets\": \"素材上传中\",\n    \"modal.header.select-files\": \"选中文件\",\n    \"modal.nav.browse\": \"浏览\",\n    \"modal.nav.computer\": \"从本地\",\n    \"modal.nav.selected\": \"选中\",\n    \"modal.nav.url\": \"从链接\",\n    \"modal.selected-list.sub-header-subtitle\": \"拖拽排序字段中的素材\",\n    \"modal.upload-list.footer.button\": \"上传 {number, plural, one {# 个素材} other {# 个素材}}到媒体库\",\n    \"modal.upload-list.sub-header-subtitle\": \"在添加到媒体库之前，管理你的素材\",\n    \"modal.upload-list.sub-header.button\": \"添加更多的素材\",\n    \"page.title\": \"设置 - 媒体库\",\n    \"plugin.description.long\": \"媒体文件管理\",\n    \"plugin.description.short\": \"媒体文件管理\",\n    \"plugin.name\": \"媒体库\",\n    \"settings.section.doc.label\": \"文档\",\n    \"search.placeholder\": \"搜索素材...\",\n    \"search.clear.label\": \"清除搜索\",\n    \"search.label\": \"搜索资源\",\n    \"settings.blockTitle\": \"资源管理\",\n    \"settings.form.autoOrientation.description\": \"根据EXIF orientation标签自动旋转图像\",\n    \"settings.form.autoOrientation.label\": \"开始自动旋转功能\",\n    \"settings.form.responsiveDimensions.description\": \"上传素材将会自动生成大，中，小三种尺寸\",\n    \"settings.form.responsiveDimensions.label\": \"启动响应式图片上传\",\n    \"settings.form.sizeOptimization.label\": \"开启图片大小优化(无损)\",\n    \"settings.form.sizeOptimization.description\": \"启用此选项将缩小图像大小并略微降低其质量。\",\n    \"settings.form.videoPreview.description\": \"自动生成6秒钟的视频预览（GIF）\",\n    \"settings.form.videoPreview.label\": \"预览\",\n    \"settings.header.label\": \"媒体库 - 设置\",\n    \"settings.section.image.label\": \"图片\",\n    \"settings.section.video.label\": \"视频\",\n    \"settings.sub-header.label\": \"媒体库设置\",\n    \"sort.created_at_asc\": \"最早上传\",\n    \"sort.created_at_desc\": \"最近上传\",\n    \"sort.label\": \"排序\",\n    \"sort.name_asc\": \"字母顺序 (A to Z)\",\n    \"sort.name_desc\": \"字母倒序 (Z to A)\",\n    \"sort.updated_at_asc\": \"最早更新\",\n    \"sort.updated_at_desc\": \"最近更新\",\n    \"window.confirm.close-modal.file\": \"你确定吗？你的更改将会丢失\",\n    \"window.confirm.close-modal.files\": \"你确定吗？你还有一些文件还没有被上传\",\n    \"apiError.FileTooBig\": \"上传的文件超过允许的最大资源大小\",\n    \"upload.generic-error\": \"上传文件时发生错误\",\n    \"bulk.select.label\": \"选择所有资源\",\n    \"content.isLoading\": \"内容正在加载中\",\n    \"control-card.stop-crop\": \"停止裁剪\",\n    \"header.actions.add-assets.folder\": \"文件夹\",\n    \"header.actions.upload-new-asset\": \"上传新资源\",\n    \"header.content.assets-empty\": \"无资源\",\n    \"input.label\": \"拖拽到此处或\",\n    \"input.notification.not-supported\": \"您无法上传此类型的文件，仅接受以下类型 - {fileTypes}\",\n    \"permissions.not-allowed.update\": \"您无权编辑此文件\",\n    \"tabs.title\": \"您希望如何上传您的资源？\",\n    \"config.title\": \"配置视图 - 媒体库\",\n    \"config.back\": \"返回\",\n    \"config.subtitle\": \"定义媒体库的视图设置\",\n    \"config.entries.title\": \"每页条目数\",\n    \"config.sort.title\": \"默认排序顺序\",\n    \"config.entries.note\": \"媒体库中默认显示的资源数量\",\n    \"config.note\": \"注意：您可以在媒体库中覆盖此值\",\n    \"config.popUpWarning.warning.updateAllSettings\": \"这将修改您的所有设置\",\n    \"view-switch.list\": \"列表视图\",\n    \"view-switch.grid\": \"网格视图\"\n};\n\nexport { zhHans as default };\n//# sourceMappingURL=zh-Hans.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AAAA,EACT,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,eAAe;AAAA,EACf,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,8BAA8B;AAAA,EAC9B,qBAAqB;AAAA,EACrB,cAAc;AAAA,EACd,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,kCAAkC;AAAA,EAClC,gCAAgC;AAAA,EAChC,oCAAoC;AAAA,EACpC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,mBAAmB;AAAA,EACnB,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,wBAAwB;AAAA,EACxB,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,kCAAkC;AAAA,EAClC,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,oCAAoC;AAAA,EACpC,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8BAA8B;AAAA,EAC9B,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,iBAAiB;AAAA,EACjB,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,cAAc;AAAA,EACd,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,6CAA6C;AAAA,EAC7C,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,gCAAgC;AAAA,EAChC,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,qBAAqB;AAAA,EACrB,qBAAqB;AAAA,EACrB,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,eAAe;AAAA,EACf,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,eAAe;AAAA,EACf,iDAAiD;AAAA,EACjD,oBAAoB;AAAA,EACpB,oBAAoB;AACxB;", "names": []}