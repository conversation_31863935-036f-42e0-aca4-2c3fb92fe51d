'use strict';
/**
 * nearby-place-category router
 */
module.exports = {
    routes: [
        // Specific routes first to avoid conflicts
        {
            method: 'GET',
            path: '/nearby-place-categories/enabled',
            handler: 'nearby-place-category.getEnabled',
            config: {
                auth: false,
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'GET',
            path: '/nearby-place-categories/google-place-types',
            handler: 'nearby-place-category.getGooglePlaceTypes',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        // Generic routes after specific ones
        {
            method: 'GET',
            path: '/nearby-place-categories',
            handler: 'nearby-place-category.find',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'GET',
            path: '/nearby-place-categories/:id',
            handler: 'nearby-place-category.findOne',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/nearby-place-categories',
            handler: 'nearby-place-category.create',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'PUT',
            path: '/nearby-place-categories/:id',
            handler: 'nearby-place-category.update',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'DELETE',
            path: '/nearby-place-categories/:id',
            handler: 'nearby-place-category.delete',
            config: {
                policies: [],
                middlewares: [],
            },
        },
    ],
};
