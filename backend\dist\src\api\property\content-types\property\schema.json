{"kind": "collectionType", "collectionName": "properties", "info": {"singularName": "property", "pluralName": "properties", "displayName": "Property", "description": "Real estate property listings"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"title": {"type": "string", "pluginOptions": {"i18n": {"localized": true}}, "required": true}, "description": {"type": "richtext", "pluginOptions": {"i18n": {"localized": true}}}, "price": {"type": "decimal", "required": true}, "currency": {"type": "enumeration", "default": "USD", "enum": ["USD", "EUR", "GBP", "AED", "SAR"]}, "propertyType": {"type": "enumeration", "required": true, "enum": ["apartment", "villa", "townhouse", "penthouse", "studio", "duplex", "land", "commercial"]}, "offer": {"type": "enumeration", "required": true, "enum": ["for-sale", "for-rent", "sold", "rented", "off-market"]}, "bedrooms": {"type": "integer", "min": 0}, "bathrooms": {"type": "integer", "min": 0}, "area": {"type": "decimal", "required": true}, "areaUnit": {"type": "enumeration", "default": "sqft", "enum": ["sqft", "sqm"]}, "address": {"type": "string", "required": true}, "city": {"type": "string", "required": true}, "country": {"type": "string", "required": true}, "neighborhood": {"type": "json", "pluginOptions": {"i18n": {"localized": true}}}, "coordinates": {"type": "json", "required": false}, "nearbyPlaces": {"type": "json", "required": false}, "latitude": {"type": "decimal"}, "longitude": {"type": "decimal"}, "propertyCode": {"type": "string", "unique": true}, "features": {"type": "json"}, "isLuxury": {"type": "boolean", "default": false}, "images": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "floorPlan": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files"]}, "virtualTour": {"type": "string"}, "yearBuilt": {"type": "integer"}, "parking": {"type": "integer", "min": 0}, "furnished": {"type": "boolean", "default": false}, "petFriendly": {"type": "boolean", "default": false}, "owner": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "properties"}, "agent": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user"}, "featured": {"type": "boolean", "default": false}, "views": {"type": "integer", "default": 0}, "slug": {"type": "uid", "targetField": "title"}, "project": {"type": "relation", "relation": "manyToOne", "target": "api::project.project", "inversedBy": "properties"}}}