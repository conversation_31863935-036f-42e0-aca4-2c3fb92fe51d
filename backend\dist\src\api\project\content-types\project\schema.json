{"kind": "collectionType", "collectionName": "projects", "info": {"singularName": "project", "pluralName": "projects", "displayName": "Project", "description": "Real estate development projects"}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"name": {"type": "string", "required": true, "pluginOptions": {"i18n": {"localized": true}}}, "description": {"type": "richtext", "pluginOptions": {"i18n": {"localized": true}}}, "developer": {"type": "string", "required": true}, "projectType": {"type": "enumeration", "enum": ["residential", "commercial", "mixed-use", "industrial"], "required": true}, "status": {"type": "enumeration", "enum": ["planning", "under-construction", "completed", "on-hold", "cancelled"], "default": "planning"}, "startDate": {"type": "date"}, "completionDate": {"type": "date"}, "totalUnits": {"type": "integer"}, "availableUnits": {"type": "integer"}, "minPrice": {"type": "decimal"}, "maxPrice": {"type": "decimal"}, "currency": {"type": "enumeration", "enum": ["USD", "EUR", "GBP", "AED", "SAR"], "default": "USD"}, "address": {"type": "string", "required": true}, "city": {"type": "string", "required": true}, "country": {"type": "string", "required": true}, "latitude": {"type": "decimal"}, "longitude": {"type": "decimal"}, "amenities": {"type": "json"}, "images": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "floorPlans": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files"]}, "brochure": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["files"]}, "virtualTour": {"type": "string"}, "paymentPlan": {"type": "richtext"}, "properties": {"type": "relation", "relation": "oneToMany", "target": "api::property.property", "mappedBy": "project"}, "featured": {"type": "boolean", "default": false}, "views": {"type": "integer", "default": 0}, "slug": {"type": "uid", "targetField": "name"}}}