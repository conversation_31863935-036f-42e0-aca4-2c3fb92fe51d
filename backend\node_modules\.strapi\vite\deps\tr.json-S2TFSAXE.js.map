{"version": 3, "sources": ["../../../@strapi/content-manager/dist/admin/translations/tr.json.mjs"], "sourcesContent": ["var groups = \"Gruplar\";\nvar models = \"Koleksiyon Tipleri\";\nvar pageNotFound = \"Sayfa bulunamadı\";\nvar tr = {\n    \"App.schemas.data-loaded\": \"Şemalar başarıyla yüklendi\",\n    \"ListViewTable.relation-loaded\": \"İlişkiler yüklendi\",\n    \"ListViewTable.relation-loading\": \"İlişkiler yükleniyor\",\n    \"ListViewTable.relation-more\": \"Bu ilişki gösterilenden daha çok kayıt içeriyor\",\n    \"EditRelations.title\": \"İlişkili Data\",\n    \"HeaderLayout.button.label-add-entry\": \"Yeni bir girdi oluştur\",\n    \"api.id\": \"API KİMLİK NO\",\n    \"apiError.This attribute must be unique\": \"{field} benzersiz olmalı\",\n    \"components.AddFilterCTA.add\": \"Filtreler\",\n    \"components.AddFilterCTA.hide\": \"Filtreler\",\n    \"components.DragHandle-label\": \"<PERSON><PERSON><PERSON><PERSON>kle\",\n    \"components.DraggableAttr.edit\": \"Düzenlemek için tıklayın\",\n    \"components.DraggableCard.delete.field\": \"Sil: {item}\",\n    \"components.DraggableCard.edit.field\": \"Düzenle: {item}\",\n    \"components.DraggableCard.move.field\": \"Taşı: {item}\",\n    \"components.ListViewTable.row-line\": \"öğe satır {number}\",\n    \"components.DynamicZone.ComponentPicker-label\": \"Bir bileşen seç\",\n    \"components.DynamicZone.add-component\": \"{componentName}'e bir bileşen ekle\",\n    \"components.DynamicZone.delete-label\": \"Sil: {name}\",\n    \"components.DynamicZone.error-message\": \"Bileşen bir ya da daha fazla hata içeriyor\",\n    \"components.DynamicZone.missing-components\": \"{number} eksik bileşen var\",\n    \"components.DynamicZone.move-down-label\": \"Bileşeni aşağı taşı\",\n    \"components.DynamicZone.move-up-label\": \"Bileşeni yukarı taşı\",\n    \"components.DynamicZone.pick-compo\": \"Bir bileşen seç\",\n    \"components.DynamicZone.required\": \"Bileşen zorunlu\",\n    \"components.EmptyAttributesBlock.button\": \"Ayarlar sayfasına git\",\n    \"components.EmptyAttributesBlock.description\": \"Ayarlarınızı değiştirebilirsiniz\",\n    \"components.FieldItem.linkToComponentLayout\": \"Bileşenin düzenini belirle\",\n    \"components.FieldSelect.label\": \"Bir alan ekle\",\n    \"components.FilterOptions.button.apply\": \"Uygula\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Uygula\",\n    \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Hepsini temizle\",\n    \"components.FiltersPickWrapper.PluginHeader.description\": \"Filtrelemek için uygulanacak şartları ayarlayın\",\n    \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Filtreler\",\n    \"components.FiltersPickWrapper.hide\": \"Gizle\",\n    \"components.LeftMenu.Search.label\": \"Bir içerik tipi ara\",\n    \"components.LeftMenu.collection-types\": \"Koleksiyon Tipleri\",\n    \"components.LeftMenu.single-types\": \"Tekil Tipler\",\n    \"components.LimitSelect.itemsPerPage\": \"Sayfa başı\",\n    \"components.NotAllowedInput.text\": \"Bu alanı görmek için iznin yok\",\n    \"components.RepeatableComponent.error-message\": \"Bileşen ya da bileşenler bir ya da daha çok hata içeriyor\",\n    \"components.Search.placeholder\": \"Kayıt aramak için...\",\n    \"components.Select.draft-info-title\": \"Durum: Taslak\",\n    \"components.Select.publish-info-title\": \"Durum: Yayında\",\n    \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Düzenleme görünümünü özelleştir\",\n    \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Liste görünümünün ayarlarını belirle.\",\n    \"components.SettingsViewWrapper.pluginHeader.title\": \"Görünümü ayarla - {name}\",\n    \"components.TableDelete.delete\": \"Hepsini sil\",\n    \"components.TableDelete.deleteSelected\": \"Silme seçildi\",\n    \"components.TableDelete.label\": \"{number} girdi seçildi\",\n    \"components.TableEmpty.withFilters\": \"Uygulanan filtrelerle {contentType} yoktur...\",\n    \"components.TableEmpty.withSearch\": \"Aramaya karşılık gelen {contentType} yoktur ({search})...\",\n    \"components.TableEmpty.withoutFilter\": \"{contentType} yoktur...\",\n    \"components.empty-repeatable\": \"Henüz bir girdi yok. Aşağıdaki butona bas ve ekle.\",\n    \"components.notification.info.maximum-requirement\": \"Alan sayı sınırına ulaştınız.\",\n    \"components.notification.info.minimum-requirement\": \"Minimum gerekleri sağlayacak bir alan eklendi\",\n    \"components.repeatable.reorder.error\": \"Bileşenin alanlarının sırasını değiştirirken bir hata oluştu. Lütfen tekrar deneyin.\",\n    \"components.reset-entry\": \"Girdiyi sıfırla\",\n    \"components.uid.apply\": \"uygula\",\n    \"components.uid.available\": \"Müsait\",\n    \"components.uid.regenerate\": \"Yeniden Üret\",\n    \"components.uid.suggested\": \"önerilen\",\n    \"components.uid.unavailable\": \"Müsait Değil\",\n    \"containers.Edit.Link.Layout\": \"Düzeni ayarla\",\n    \"containers.Edit.Link.Model\": \"Koleksiyon-tipini düzenle\",\n    \"containers.Edit.addAnItem\": \"Bir öğe ekle...\",\n    \"containers.Edit.clickToJump\": \"Kayıta atlamak için tıklayın\",\n    \"containers.Edit.delete\": \"Sil\",\n    \"containers.Edit.editing\": \"Düzenleniyor...\",\n    \"containers.Edit.information\": \"Bilgi\",\n    \"containers.Edit.information.by\": \"Tarafından\",\n    \"containers.Edit.information.created\": \"Oluşturuldu\",\n    \"containers.Edit.information.draftVersion\": \"taslak versiyonu\",\n    \"containers.Edit.information.editing\": \"Düzenleniyor\",\n    \"containers.Edit.information.lastUpdate\": \"Son güncelleme\",\n    \"containers.Edit.information.publishedVersion\": \"yayınlanan versiyonu\",\n    \"containers.Edit.pluginHeader.title.new\": \"Bir girdi oluştur\",\n    \"containers.Edit.reset\": \"Reset\",\n    \"containers.Edit.returnList\": \"Listeye dön\",\n    \"containers.Edit.seeDetails\": \"Detaylar\",\n    \"containers.Edit.submit\": \"Kaydet\",\n    \"containers.EditSettingsView.modal-form.edit-field\": \"Alanı düzenle\",\n    \"containers.EditView.add.new-entry\": \"Bir girdi ekle\",\n    \"containers.Home.introduction\": \"Girişlerinizi düzenlemek için soldaki menüdeki ilgili bağlantıya gidin. Bu eklentinin ayarları düzenlemek için uygun bir yol bulunmamaktadır ve halen aktif geliştirme aşamasındadır.\",\n    \"containers.Home.pluginHeaderDescription\": \"Güçlü ve güzel bir arayüz aracılığıyla girişlerinizi yönetin.\",\n    \"containers.Home.pluginHeaderTitle\": \"İçerik Yönetimi\",\n    \"containers.List.draft\": \"Taslak\",\n    \"containers.List.errorFetchRecords\": \"Hata\",\n    \"containers.List.published\": \"Yayınlandı\",\n    \"containers.list.displayedFields\": \"Görüntülenen Alanlar\",\n    \"containers.list.items\": \"{number} öğe\",\n    \"containers.list.table-headers.publishedAt\": \"Durum\",\n    \"containers.ListSettingsView.modal-form.edit-label\": \"Düzenle: {fieldName}\",\n    \"containers.SettingPage.add.field\": \"Bir başka Alan ekle\",\n    \"containers.SettingPage.attributes\": \"Nitelik alanları\",\n    \"containers.SettingPage.attributes.description\": \"Niteliklerin sırasını tanımlayın\",\n    \"containers.SettingPage.editSettings.description\": \"Yerleşimi oluşturmak için alanları sürükleyip bırakın\",\n    \"containers.SettingPage.editSettings.entry.title\": \"Girdi başlığı\",\n    \"containers.SettingPage.editSettings.entry.title.description\": \"Girdinin görüntülenen alanını belirle\",\n    \"containers.SettingPage.editSettings.relation-field.description\": \"Düzenleme ve listeleme görünümünde görüntülenecek alanı belirle\",\n    \"containers.SettingPage.editSettings.title\": \"Düzenle (ayarlar)\",\n    \"containers.SettingPage.layout\": \"Düzen\",\n    \"containers.SettingPage.listSettings.description\": \"Bu koleksiyon tipi için seçenekleri düzenle\",\n    \"containers.SettingPage.listSettings.title\": \"Liste (ayarlar)\",\n    \"containers.SettingPage.pluginHeaderDescription\": \"Bu koleksiyon tipi için özel ayarlı düzenle\",\n    \"containers.SettingPage.settings\": \"Ayarlar\",\n    \"containers.SettingPage.view\": \"Görüntüle\",\n    \"containers.SettingViewModel.pluginHeader.title\": \"İçerik Yöneticisi - {name}\",\n    \"containers.SettingsPage.Block.contentType.description\": \"Belirli ayarları yapılandırın\",\n    \"containers.SettingsPage.Block.contentType.title\": \"Koleksiyon Tipleri\",\n    \"containers.SettingsPage.Block.generalSettings.description\": \"Koleksiyon tiplerin için varsayılan seçenekleri düzenle\",\n    \"containers.SettingsPage.Block.generalSettings.title\": \"Genel\",\n    \"containers.SettingsPage.pluginHeaderDescription\": \"Tüm Koleksiyon Tiplerin ve Gruplarının ayarlarını düzenle\",\n    \"containers.SettingsView.list.subtitle\": \"Koleksiyon ve Gruplarının düzen ve görünümlerini düzenle\",\n    \"containers.SettingsView.list.title\": \"Düzenlemelerini görüntüle\",\n    \"edit-settings-view.link-to-ctb.components\": \"Bileşeni düzenle\",\n    \"edit-settings-view.link-to-ctb.content-types\": \"İçerik tipini düzenle\",\n    \"emptyAttributes.button\": \"Koleksiyon Tipi kurucusuna git\",\n    \"emptyAttributes.description\": \"Koleksiyon Tipine ilk alanı ekle\",\n    \"emptyAttributes.title\": \"Henüz bir alan yok\",\n    \"error.attribute.key.taken\": \"Bu değer zaten var.\",\n    \"error.attribute.sameKeyAndName\": \"Eşit olamaz\",\n    \"error.attribute.taken\": \"Bu alan ismi zaten var.\",\n    \"error.contentTypeName.taken\": \"Bu alan ismi zaten var.\",\n    \"error.model.fetch\": \"Modellerin yapılandırması getirilirken bir hata oluştu.\",\n    \"error.record.create\": \"Kayıt oluşturulurken bir hata oluştu.\",\n    \"error.record.delete\": \"Kayıt silinirken bir hata oluştu.\",\n    \"error.record.fetch\": \"Kayıt getirilirken bir hata oluştu.\",\n    \"error.record.update\": \"Kayıt güncelleme sırasında bir hata oluştu.\",\n    \"error.records.count\": \"Sayım kayıtları getirilinceye kadar\",\n    \"error.records.fetch\": \"Kayıtlar getirilirken bir hata oluştu.\",\n    \"error.schema.generation\": \"Şema oluşturma sırasında bir hata oluştu.\",\n    \"error.validation.json\": \"Bu JSON biçimi ile eşleşmiyor\",\n    \"error.validation.max\": \"Değer çok yüksek.\",\n    \"error.validation.maxLength\": \"Değer çok uzun.\",\n    \"error.validation.min\": \"Değer çok az.\",\n    \"error.validation.minLength\": \"Değer çok kısa.\",\n    \"error.validation.minSupMax\": \"Üstü olamaz\",\n    \"error.validation.regex\": \"Regex ile eşleşmiyor.\",\n    \"error.validation.required\": \"Zorunlu alandır.\",\n    \"form.Input.bulkActions\": \"Toplu işlemleri etkinleştir\",\n    \"form.Input.defaultSort\": \"Varsayılan sıralama özelliği\",\n    \"form.Input.description\": \"Açıklama\",\n    \"form.Input.description.placeholder\": \"Profildeki görünen ad\",\n    \"form.Input.editable\": \"Düzenlenebilir alan\",\n    \"form.Input.filters\": \"Filtreleri etkinleştir\",\n    \"form.Input.label\": \"Etiket\",\n    \"form.Input.label.inputDescription\": \"Bu değer, tablonun başında görüntülenen etiketi geçersiz kılar\",\n    \"form.Input.pageEntries\": \"Sayfa başına kayıtlar\",\n    \"form.Input.placeholder\": \"Placeholder\",\n    \"form.Input.placeholder.placeholder\": \"Müthiş değerim\",\n    \"form.Input.search\": \"Aramayı etkinleştir\",\n    \"form.Input.search.field\": \"Bu alanda aramayı etkinleştir\",\n    \"form.Input.sort.field\": \"Bu alana göre sıralamayı etkinleştir\",\n    \"form.Input.sort.order\": \"Varsayılan sıralama\",\n    \"form.Input.wysiwyg\": \"WYSIWYG olarak görüntüle\",\n    \"global.displayedFields\": \"Görüntülenen Alanlar\",\n    groups: groups,\n    \"groups.numbered\": \"Gruplar ({number})\",\n    \"header.name\": \"İçerik\",\n    \"link-to-ctb\": \"Modeli düzenle\",\n    models: models,\n    \"models.numbered\": \"Koleksiyon Tipleri ({number})\",\n    \"notification.error.displayedFields\": \"En az bir görüntülenen alana ihtiyacınız var\",\n    \"notification.error.relationship.fetch\": \"İlişki getirme sırasında bir hata oluştu.\",\n    \"notification.info.SettingPage.disableSort\": \"Sıralamaya izin verilen tek bir özelliğe sahip olmanız gerekir\",\n    \"notification.info.minimumFields\": \"En az bir alan görüntülenebilir olmalı\",\n    \"notification.upload.error\": \"Dosyalarını yüklerken bir hata oluştu\",\n    pageNotFound: pageNotFound,\n    \"pages.ListView.header-subtitle\": \"{number} girdi bulundu\",\n    \"pages.NoContentType.button\": \"İlk İçerik Tipini oluştur\",\n    \"pages.NoContentType.text\": \"Henüz hiç içeriğin yok. Bir İçerik Tipi oluşturarak işe başlamanı öneririz.\",\n    \"permissions.not-allowed.create\": \"Belge oluşturma iznin yok\",\n    \"permissions.not-allowed.update\": \"Bu belgeyi görme iznin yok\",\n    \"plugin.description.long\": \"Veritabanındaki verileri görmek, düzenlemek ve silmek için hızlı bir yol.\",\n    \"plugin.description.short\": \"Veritabanındaki verileri görmek, düzenlemek ve silmek için hızlı bir yol.\",\n    \"popUpWarning.warning.has-draft-relations.title\": \"Onay\",\n    \"popUpWarning.warning.publish-question\": \"Hala yayınlamak istiyor musun?\",\n    \"popUpwarning.warning.has-draft-relations.button-confirm\": \"Evet, yayınla\",\n    \"popUpwarning.warning.has-draft-relations.message\": \"<b>{count} ilişki henüz yayınlanmadı ve bu beklenmedik bir davranışa yol açabilir.\",\n    \"popover.display-relations.label\": \"İlişkileri göster\",\n    \"select.currently.selected\": \"{count} tane seçili\",\n    \"success.record.delete\": \"Silindi\",\n    \"success.record.publish\": \"Yayınlandı\",\n    \"success.record.save\": \"Kaydedildi\",\n    \"success.record.unpublish\": \"Yayından Kaldırıldı\",\n    \"utils.data-loaded\": \"{number} girdi başarıyla yüklendi\"\n};\n\nexport { tr as default, groups, models, pageNotFound };\n//# sourceMappingURL=tr.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,eAAe;AACnB,IAAI,KAAK;AAAA,EACL,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,0CAA0C;AAAA,EAC1C,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,2DAA2D;AAAA,EAC3D,oDAAoD;AAAA,EACpD,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,qBAAqB;AACzB;", "names": []}