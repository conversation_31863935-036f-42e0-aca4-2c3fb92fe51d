'use client';

import React, { useState, useEffect } from 'react';
import DashboardLayout from '@/components/Dashboard/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import { propertiesAPI } from '@/lib/api';
import {
  Building2,
  MessageSquare,
  Heart,
  TrendingUp,
  Eye,
  DollarSign,
  Calendar,
  MapPin,
  Plus,
  User
} from 'lucide-react';
import Link from 'next/link';

interface DashboardStats {
  totalProperties: number;
  publishedProperties: number;
  draftProperties: number;
  totalViews: number;
  totalMessages: number;
  favoriteProperties: number;
}

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalProperties: 0,
    publishedProperties: 0,
    draftProperties: 0,
    totalViews: 0,
    totalMessages: 0,
    favoriteProperties: 0,
  });
  const [recentProperties, setRecentProperties] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch user's properties
        const properties = await propertiesAPI.getMyProperties();
        const published = properties.filter((p: any) => p.publishedAt);
        const drafts = properties.filter((p: any) => !p.publishedAt);

        setStats({
          totalProperties: properties.length,
          publishedProperties: published.length,
          draftProperties: drafts.length,
          totalViews: properties.reduce((sum: number, p: any) => sum + (p.views || 0), 0),
          totalMessages: 0, // TODO: Implement messaging
          favoriteProperties: 0, // TODO: Implement favorites
        });

        setRecentProperties(properties.slice(0, 3));
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
        // If it's a 403 error, show a message to the user
        if (error.response?.status === 403) {
          console.log('User not properly authenticated');
        }
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchDashboardData();
    } else {
      setLoading(false);
    }
  }, [user]);

  const statCards = [
    {
      title: 'Total Properties',
      value: stats.totalProperties,
      icon: Building2,
      color: 'blue',
      description: `${stats.publishedProperties} published, ${stats.draftProperties} drafts`
    },
    {
      title: 'Total Views',
      value: stats.totalViews,
      icon: Eye,
      color: 'green',
      description: 'Across all properties'
    },
    {
      title: 'Messages',
      value: stats.totalMessages,
      icon: MessageSquare,
      color: 'purple',
      description: 'Unread conversations'
    },
    {
      title: 'Favorites',
      value: stats.favoriteProperties,
      icon: Heart,
      color: 'red',
      description: 'Saved properties'
    }
  ];

  const quickActions = [
    {
      title: 'Submit Property',
      description: 'List a new property',
      href: '/submit-property',
      icon: Plus,
      color: 'blue'
    },
    {
      title: 'Browse Properties',
      description: 'Explore available properties',
      href: '/properties',
      icon: Building2,
      color: 'green'
    },
    {
      title: 'View Analytics',
      description: 'Check your property performance',
      href: '/dashboard/analytics',
      icon: TrendingUp,
      color: 'purple'
    },
    {
      title: 'Manage Profile',
      description: 'Update your information',
      href: '/dashboard/profile',
      icon: User,
      color: 'gray'
    }
  ];

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-6 text-white">
          <h1 className="text-3xl font-bold mb-2">
            Welcome back, {user?.firstName || user?.username || 'User'}!
          </h1>
          <p className="text-blue-100">
            Here's what's happening with your properties today.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {statCards.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
                  </div>
                  <div className={`p-3 rounded-full bg-${stat.color}-100`}>
                    <Icon className={`h-6 w-6 text-${stat.color}-600`} />
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => {
              const Icon = action.icon;
              return (
                <Link
                  key={index}
                  href={action.href}
                  className="group p-4 rounded-lg border-2 border-dashed border-gray-300 hover:border-blue-500 hover:bg-blue-50 transition-all duration-200"
                >
                  <div className="text-center">
                    <Icon className="h-8 w-8 mx-auto mb-2 text-gray-400 group-hover:text-blue-600" />
                    <h3 className="font-medium text-gray-900 group-hover:text-gray-800">
                      {action.title}
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      {action.description}
                    </p>
                  </div>
                </Link>
              );
            })}
          </div>
        </div>

        {/* Recent Properties */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-900">Recent Properties</h2>
            <Link
              href="/dashboard/properties"
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              View all →
            </Link>
          </div>

          {recentProperties.length > 0 ? (
            <div className="space-y-4">
              {recentProperties.map((property, index) => (
                <div key={index} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                  <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                    <Building2 className="h-8 w-8 text-gray-400" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{property.title}</h3>
                    <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                      <span className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {property.city}, {property.country}
                      </span>
                      <span className="flex items-center">
                        <DollarSign className="h-4 w-4 mr-1" />
                        {property.price?.toLocaleString()} {property.currency}
                      </span>
                      <span className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(property.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className={`
                      inline-flex px-2 py-1 text-xs font-medium rounded-full
                      ${property.publishedAt
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                      }
                    `}>
                      {property.publishedAt ? 'Published' : 'Draft'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No properties yet</h3>
              <p className="text-gray-500 mb-4">Get started by submitting your first property.</p>
              <Link
                href="/submit-property"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Submit Property
              </Link>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
};

export default DashboardPage;
