// Script to create test data through Strapi admin
const axios = require('axios');

const ADMIN_API_BASE = 'http://localhost:1337/admin';
const API_BASE = 'http://localhost:1337/api';

// Admin credentials (you'll need to update these)
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'password123';

// Test properties data
const testProperties = [
  {
    title: 'Modern Downtown Apartment',
    description: 'Beautiful 2-bedroom apartment in the heart of downtown with stunning city views.',
    price: 450000,
    currency: 'USD',
    propertyType: 'apartment',
    status: 'for_sale',
    bedrooms: 2,
    bathrooms: 2,
    area: 1200,
    address: '123 Main Street, Downtown',
    city: 'New York',
    state: 'NY',
    zipCode: '10001',
    country: 'USA',
    neighborhood: ['Downtown', 'Business District'],
    features: ['balcony', 'parking', 'gym', 'concierge'],
    yearBuilt: 2020,
    publishedAt: new Date().toISOString()
  },
  {
    title: 'Luxury Waterfront Villa',
    description: 'Stunning 4-bedroom villa with private beach access and panoramic ocean views.',
    price: 1250000,
    currency: 'USD',
    propertyType: 'house',
    status: 'for_sale',
    bedrooms: 4,
    bathrooms: 3,
    area: 3500,
    address: '456 Ocean Drive, Waterfront',
    city: 'Miami',
    state: 'FL',
    zipCode: '33101',
    country: 'USA',
    neighborhood: ['Waterfront', 'Luxury District'],
    features: ['pool', 'garden', 'garage', 'security'],
    yearBuilt: 2018,
    publishedAt: new Date().toISOString()
  },
  {
    title: 'Cozy Family Home',
    description: 'Perfect 3-bedroom family home in a quiet residential neighborhood.',
    price: 320000,
    currency: 'USD',
    propertyType: 'house',
    status: 'for_sale',
    bedrooms: 3,
    bathrooms: 2,
    area: 1800,
    address: '789 Elm Street, Residential Area',
    city: 'Austin',
    state: 'TX',
    zipCode: '73301',
    country: 'USA',
    neighborhood: ['Residential Area', 'Family Neighborhood'],
    features: ['garden', 'garage', 'fireplace'],
    yearBuilt: 2015,
    publishedAt: new Date().toISOString()
  },
  {
    title: 'Historic District Townhouse',
    description: 'Charming 2-bedroom townhouse in the historic district with original features.',
    price: 580000,
    currency: 'USD',
    propertyType: 'townhouse',
    status: 'for_sale',
    bedrooms: 2,
    bathrooms: 1,
    area: 1400,
    address: '321 Heritage Lane, Historic District',
    city: 'Boston',
    state: 'MA',
    zipCode: '02101',
    country: 'USA',
    neighborhood: ['Historic District', 'Arts District'],
    features: ['fireplace', 'hardwood_floors', 'original_features'],
    yearBuilt: 1890,
    publishedAt: new Date().toISOString()
  },
  {
    title: 'Modern Penthouse Suite',
    description: 'Luxurious penthouse with rooftop terrace and 360-degree city views.',
    price: 2100000,
    currency: 'USD',
    propertyType: 'apartment',
    status: 'for_sale',
    bedrooms: 3,
    bathrooms: 3,
    area: 2800,
    address: '555 Sky Tower, Uptown',
    city: 'Chicago',
    state: 'IL',
    zipCode: '60601',
    country: 'USA',
    neighborhood: ['Uptown', 'Luxury District'],
    features: ['terrace', 'elevator', 'concierge', 'gym', 'parking'],
    yearBuilt: 2022,
    publishedAt: new Date().toISOString()
  }
];

async function createAdminData() {
  try {
    console.log('🔐 Logging in as admin...');
    
    // Login as admin
    const loginResponse = await axios.post(`${ADMIN_API_BASE}/login`, {
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD
    });
    
    const adminToken = loginResponse.data.data.token;
    console.log('✅ Admin login successful');
    
    // Set up axios with admin token
    const adminApi = axios.create({
      baseURL: ADMIN_API_BASE,
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    // First, let's try to find or create user 'badr'
    let userId = null;
    try {
      console.log('👤 Looking for user badr...');
      const usersResponse = await adminApi.get('/users');
      const badrUser = usersResponse.data.results.find(user => 
        user.username === 'badr' || user.email === '<EMAIL>'
      );
      
      if (badrUser) {
        userId = badrUser.id;
        console.log(`✅ Found user badr with ID: ${userId}`);
      } else {
        console.log('❌ User badr not found, using admin user');
        userId = 1; // Use admin user as fallback
      }
    } catch (error) {
      console.log('❌ Could not fetch users, using default ID');
      userId = 1;
    }
    
    // Create properties
    console.log('🏠 Creating test properties...');
    for (let i = 0; i < testProperties.length; i++) {
      const property = testProperties[i];
      
      try {
        const response = await adminApi.post('/content-manager/collection-types/api::property.property', {
          ...property,
          owner: userId
        });
        
        console.log(`✅ Created property: ${property.title}`);
      } catch (error) {
        console.error(`❌ Failed to create property: ${property.title}`);
        console.error('Error:', error.response?.data || error.message);
      }
    }
    
    console.log('✅ Admin data creation completed!');
    
  } catch (error) {
    console.error('❌ Error creating admin data:', error.response?.data || error.message);
    console.log('\n📝 Note: Make sure to:');
    console.log('1. Update ADMIN_EMAIL and ADMIN_PASSWORD in this script');
    console.log('2. Ensure Strapi admin is running on http://localhost:1337');
    console.log('3. Create an admin user if one doesn\'t exist');
  }
}

// Run the script
createAdminData();
