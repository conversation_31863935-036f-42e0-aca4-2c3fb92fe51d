{"kind": "collectionType", "collectionName": "nearby_place_categories", "info": {"singularName": "nearby-place-category", "pluralName": "nearby-place-categories", "displayName": "Nearby Place Category", "description": "Categories for nearby places like restaurants, schools, etc."}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true, "unique": true}, "displayName": {"type": "string", "required": true}, "description": {"type": "text"}, "icon": {"type": "string", "required": true}, "googlePlaceTypes": {"type": "json", "required": true}, "enabled": {"type": "boolean", "default": true}, "searchRadius": {"type": "integer", "default": 1000, "min": 100, "max": 5000}, "maxResults": {"type": "integer", "default": 10, "min": 1, "max": 20}, "priority": {"type": "integer", "default": 0}, "color": {"type": "string", "default": "#3B82F6"}}}