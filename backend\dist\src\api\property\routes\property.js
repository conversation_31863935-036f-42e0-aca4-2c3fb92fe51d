"use strict";
/**
 * property router
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreRouter('api::property.property', {
    config: {
        find: {
            middlewares: [],
        },
        findOne: {
            middlewares: [],
        },
        create: {
            middlewares: [],
        },
        update: {
            middlewares: [],
        },
        delete: {
            middlewares: [],
        },
    },
});
