"use strict";
/**
 * message router
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.customRoutes = void 0;
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreRouter('api::message.message');
// Custom routes
exports.customRoutes = {
    routes: [
        {
            method: 'GET',
            path: '/messages/inbox',
            handler: 'message.inbox',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'GET',
            path: '/messages/sent',
            handler: 'message.sent',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'PUT',
            path: '/messages/:id/mark-as-read',
            handler: 'message.markAsRead',
            config: {
                policies: [],
                middlewares: [],
            },
        },
    ],
};
