'use client';

import React, { useState } from 'react';
import Layout from '@/components/Layout/Layout';
import { propertiesAPI, authAPI } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';

const TestPropertyPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testPropertyCreation = async () => {
    setLoading(true);
    setError(null);
    setResult(null);
    addTestResult('🏠 Starting property creation test...');

    try {
      const testProperty = {
        title: 'Test Property - ' + new Date().toISOString(),
        description: 'This is a test property created for testing the API',
        price: 150000,
        currency: 'USD',
        propertyType: 'apartment',
        status: 'for-sale',
        bedrooms: 2,
        bathrooms: 1,
        area: 85,
        areaUnit: 'sqm',
        address: '123 Test Street',
        city: 'Test City',
        country: 'Test Country',
        neighborhood: 'Test Neighborhood',
        propertyCode: 'TEST-' + Date.now(),
        isLuxury: false,
        features: ['Balcony', 'Parking'],
        furnished: false,
        petFriendly: true,
      };

      addTestResult('📝 Sending property data to API...');
      console.log('Creating property with data:', testProperty);
      const response = await propertiesAPI.create(testProperty);
      console.log('Property creation response:', response);
      setResult(response);
      addTestResult('✅ Property created successfully!');
      addTestResult(`Property ID: ${response.data.id}`);
      addTestResult(`Property Title: ${response.data.title}`);
    } catch (err: any) {
      console.error('Property creation error:', err);
      const errorMessage = err.response?.data?.error?.message || err.message || 'Failed to create property';
      setError(errorMessage);
      addTestResult(`❌ Property creation failed: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  };

  const testFetchMyProperties = async () => {
    addTestResult('📋 Testing fetch my properties...');
    try {
      const response = await propertiesAPI.getMyProperties();
      addTestResult(`✅ Fetched ${response.data.length} user properties`);
      response.data.forEach((property: any, index: number) => {
        addTestResult(`${index + 1}. ${property.title} (${property.publishedAt ? 'Published' : 'Draft'})`);
      });
    } catch (err: any) {
      const errorMessage = err.response?.data?.error?.message || err.message;
      addTestResult(`❌ Fetch my properties failed: ${errorMessage}`);
    }
  };

  const clearTestResults = () => {
    setTestResults([]);
    setResult(null);
    setError(null);
  };

  const testPropertyPublish = async (propertyId: string) => {
    setLoading(true);
    setError(null);

    try {
      console.log('Publishing property with ID:', propertyId);
      const response = await propertiesAPI.publish(propertyId);
      console.log('Property publish response:', response);
      setResult(response);
    } catch (err: any) {
      console.error('Property publish error:', err);
      setError(err.response?.data?.error?.message || err.message || 'Failed to publish property');
    } finally {
      setLoading(false);
    }
  };

  const testGetMyProperties = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('Fetching my properties...');
      const response = await propertiesAPI.getMyProperties();
      console.log('My properties response:', response);
      setResult(response);
    } catch (err: any) {
      console.error('Get my properties error:', err);
      setError(err.response?.data?.error?.message || err.message || 'Failed to fetch properties');
    } finally {
      setLoading(false);
    }
  };

  const testGetAllProperties = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('Fetching all properties...');
      const response = await propertiesAPI.getAll();
      console.log('All properties response:', response);
      setResult(response);
    } catch (err: any) {
      console.error('Get all properties error:', err);
      setError(err.response?.data?.error?.message || err.message || 'Failed to fetch all properties');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Property API Testing</h1>
              <p className="text-gray-600">Test property creation, publishing, and retrieval</p>
            </div>

            {/* User Status */}
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h3 className="font-semibold text-blue-900 mb-2">Authentication Status</h3>
              {isAuthenticated ? (
                <div className="text-blue-800">
                  <p>✅ Authenticated as: {user?.username || user?.email}</p>
                  <p>User ID: {user?.id}</p>
                </div>
              ) : (
                <p className="text-red-600">❌ Not authenticated - Please log in first</p>
              )}
            </div>

            {/* Test Buttons */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
              <button
                onClick={testGetAllProperties}
                disabled={loading}
                className="bg-green-600 text-white py-3 px-6 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
              >
                {loading ? 'Testing...' : 'Test Get All Properties'}
              </button>

              <button
                onClick={testGetMyProperties}
                disabled={loading || !isAuthenticated}
                className="bg-blue-600 text-white py-3 px-6 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {loading ? 'Testing...' : 'Test Get My Properties'}
              </button>

              <button
                onClick={testPropertyCreation}
                disabled={loading || !isAuthenticated}
                className="bg-purple-600 text-white py-3 px-6 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Test Create Property'}
              </button>

              <button
                onClick={() => {
                  const propertyId = prompt('Enter property ID to publish:');
                  if (propertyId) testPropertyPublish(propertyId);
                }}
                disabled={loading || !isAuthenticated}
                className="bg-orange-600 text-white py-3 px-6 rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50"
              >
                {loading ? 'Publishing...' : 'Test Publish Property'}
              </button>

              <button
                onClick={clearTestResults}
                disabled={loading}
                className="bg-gray-600 text-white py-3 px-6 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50"
              >
                🗑️ Clear Results
              </button>
            </div>

            {/* Test Results Console */}
            {testResults.length > 0 && (
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">🧪 Test Results</h3>
                <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm max-h-64 overflow-y-auto">
                  {testResults.map((result, index) => (
                    <div key={index} className="mb-1">
                      {result}
                    </div>
                  ))}
                  {loading && (
                    <div className="text-yellow-400 animate-pulse">
                      Running test...
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
                <h3 className="font-semibold text-red-900 mb-2">Error</h3>
                <p className="text-red-600">{error}</p>
              </div>
            )}

            {/* Result Display */}
            {result && (
              <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
                <h3 className="font-semibold text-gray-900 mb-2">API Response</h3>
                <pre className="text-sm text-gray-600 overflow-auto max-h-96">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            )}

            {/* Instructions */}
            <div className="mt-8 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <h3 className="font-semibold text-yellow-900 mb-2">Testing Instructions</h3>
              <div className="text-yellow-800 space-y-2">
                <p><strong>Step 1:</strong> Make sure you're logged in (check authentication status above)</p>
                <p><strong>Step 2:</strong> Test "Get All Properties" to check if basic API access works</p>
                <p><strong>Step 3:</strong> Test "Create Property" to create a new property</p>
                <p><strong>Step 4:</strong> Use the property ID from the creation response to test publishing</p>
                <p><strong>Step 5:</strong> Test "Get My Properties" to see your created properties</p>
              </div>
            </div>

            {/* Permissions Note */}
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <h3 className="font-semibold text-red-900 mb-2">⚠️ Strapi Permissions Required</h3>
              <div className="text-red-800 space-y-2">
                <p>If you get 403 Forbidden errors, you need to configure Strapi permissions:</p>
                <ol className="list-decimal list-inside space-y-1 ml-4">
                  <li>Go to <a href="http://localhost:1337/admin" target="_blank" className="underline">Strapi Admin</a></li>
                  <li>Navigate to Settings → Users & Permissions Plugin → Roles</li>
                  <li>Edit "Public" role: Enable Property find, findOne</li>
                  <li>Edit "Authenticated" role: Enable Property find, findOne, create, update, delete</li>
                  <li>Save changes and test again</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default TestPropertyPage;
