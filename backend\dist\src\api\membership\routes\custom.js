"use strict";
/**
 * membership custom routes
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = {
    routes: [
        {
            method: 'POST',
            path: '/memberships/subscribe',
            handler: 'api::membership.membership.subscribe',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'GET',
            path: '/memberships/my-membership',
            handler: 'api::membership.membership.myMembership',
            config: {
                policies: [],
                middlewares: [],
            },
        },
    ],
};
