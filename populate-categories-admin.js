// Script to populate nearby place categories via admin API
const axios = require('axios');
const { GOOGLE_PLACE_TYPES } = require('./backend/src/api/nearby-place-category/services/google-place-types');

const API_BASE = 'http://localhost:1337/api';

// Admin credentials (you'll need to set these)
const adminCredentials = {
  email: '<EMAIL>', // Replace with your admin email
  password: 'admin123' // Replace with your admin password
};

const defaultCategories = [
  {
    name: 'education',
    displayName: 'Education',
    description: 'Schools, universities, libraries, and educational institutions',
    icon: '🎓',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.SCHOOL,
      GOOGLE_PLACE_TYPES.PRIMARY_SCHOOL,
      GOOGLE_PLACE_TYPES.SECONDARY_SCHOOL,
      GOOGLE_PLACE_TYPES.UNIVERSITY,
      GOOGLE_PLACE_TYPES.LIBRARY
    ],
    enabled: true,
    searchRadius: 2000,
    maxResults: 10,
    priority: 10,
    color: '#10B981'
  },
  {
    name: 'restaurants',
    displayName: 'Restaurants & Food',
    description: 'Restaurants, cafes, bars, and food establishments',
    icon: '🍽️',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.RESTAURANT,
      GOOGLE_PLACE_TYPES.CAFE,
      GOOGLE_PLACE_TYPES.BAR,
      GOOGLE_PLACE_TYPES.BAKERY,
      GOOGLE_PLACE_TYPES.MEAL_DELIVERY,
      GOOGLE_PLACE_TYPES.MEAL_TAKEAWAY
    ],
    enabled: true,
    searchRadius: 1000,
    maxResults: 15,
    priority: 9,
    color: '#F59E0B'
  },
  {
    name: 'shopping',
    displayName: 'Shopping',
    description: 'Shopping malls, stores, supermarkets, and retail',
    icon: '🛍️',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.SHOPPING_MALL,
      GOOGLE_PLACE_TYPES.SUPERMARKET,
      GOOGLE_PLACE_TYPES.CONVENIENCE_STORE,
      GOOGLE_PLACE_TYPES.DEPARTMENT_STORE,
      GOOGLE_PLACE_TYPES.CLOTHING_STORE,
      GOOGLE_PLACE_TYPES.ELECTRONICS_STORE,
      GOOGLE_PLACE_TYPES.STORE
    ],
    enabled: true,
    searchRadius: 1500,
    maxResults: 10,
    priority: 8,
    color: '#8B5CF6'
  },
  {
    name: 'healthcare',
    displayName: 'Healthcare',
    description: 'Hospitals, clinics, pharmacies, and medical facilities',
    icon: '🏥',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.HOSPITAL,
      GOOGLE_PLACE_TYPES.DOCTOR,
      GOOGLE_PLACE_TYPES.DENTIST,
      GOOGLE_PLACE_TYPES.PHARMACY,
      GOOGLE_PLACE_TYPES.PHYSIOTHERAPIST,
      GOOGLE_PLACE_TYPES.VETERINARY_CARE
    ],
    enabled: true,
    searchRadius: 2000,
    maxResults: 8,
    priority: 9,
    color: '#EF4444'
  },
  {
    name: 'transportation',
    displayName: 'Transportation',
    description: 'Bus stops, train stations, airports, and transit hubs',
    icon: '🚌',
    googlePlaceTypes: [
      GOOGLE_PLACE_TYPES.BUS_STATION,
      GOOGLE_PLACE_TYPES.TRAIN_STATION,
      GOOGLE_PLACE_TYPES.SUBWAY_STATION,
      GOOGLE_PLACE_TYPES.LIGHT_RAIL_STATION,
      GOOGLE_PLACE_TYPES.TRANSIT_STATION,
      GOOGLE_PLACE_TYPES.TAXI_STAND,
      GOOGLE_PLACE_TYPES.AIRPORT,
      GOOGLE_PLACE_TYPES.GAS_STATION
    ],
    enabled: true,
    searchRadius: 2000,
    maxResults: 8,
    priority: 7,
    color: '#3B82F6'
  }
];

async function populateCategoriesViaAdmin() {
  try {
    console.log('🔐 Logging in as admin...');

    // Try to login with admin credentials
    let adminToken;
    try {
      const loginResponse = await axios.post(`${API_BASE}/auth/local`, {
        identifier: adminCredentials.email,
        password: adminCredentials.password
      });
      adminToken = loginResponse.data.jwt;
      console.log('✅ Admin login successful');
    } catch (error) {
      console.log('❌ Admin login failed. Please check credentials or create admin user first.');
      console.log('Error:', error.response?.data?.error?.message || error.message);
      console.log('\n💡 To create an admin user, go to: http://localhost:1337/admin');
      return;
    }

    console.log('\n📋 Creating place categories...');

    for (const category of defaultCategories) {
      try {
        const response = await axios.post(`${API_BASE}/nearby-place-categories`, {
          data: category
        }, {
          headers: {
            Authorization: `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          }
        });
        
        console.log(`✅ Created category: ${category.displayName}`);
      } catch (error) {
        if (error.response?.status === 400 && error.response?.data?.error?.message?.includes('unique')) {
          console.log(`⚠️  Category already exists: ${category.displayName}`);
        } else {
          console.error(`❌ Failed to create category ${category.displayName}:`, error.response?.data?.error?.message || error.message);
        }
      }
    }

    console.log('\n🎉 Place categories setup complete!');
    
  } catch (error) {
    console.error('❌ Setup failed:', error.message);
  }
}

// Alternative: Create categories directly via Strapi service
async function createCategoriesDirectly() {
  console.log('📋 Creating categories directly via Strapi service...');
  
  try {
    // This would require running within Strapi context
    // For now, we'll provide manual instructions
    console.log('\n📝 Manual Setup Instructions:');
    console.log('1. Go to http://localhost:1337/admin');
    console.log('2. Navigate to Content Manager > Nearby Place Categories');
    console.log('3. Create the following categories:');
    
    defaultCategories.forEach((category, index) => {
      console.log(`\n${index + 1}. ${category.displayName}:`);
      console.log(`   - Name: ${category.name}`);
      console.log(`   - Display Name: ${category.displayName}`);
      console.log(`   - Description: ${category.description}`);
      console.log(`   - Icon: ${category.icon}`);
      console.log(`   - Enabled: ${category.enabled}`);
      console.log(`   - Search Radius: ${category.searchRadius}`);
      console.log(`   - Max Results: ${category.maxResults}`);
      console.log(`   - Priority: ${category.priority}`);
      console.log(`   - Color: ${category.color}`);
      console.log(`   - Google Place Types: ${JSON.stringify(category.googlePlaceTypes)}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Check if admin credentials are provided
if (adminCredentials.email === '<EMAIL>') {
  console.log('⚠️  Please update admin credentials in the script first, or use manual setup.');
  createCategoriesDirectly();
} else {
  populateCategoriesViaAdmin();
}
