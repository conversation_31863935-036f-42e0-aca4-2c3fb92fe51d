{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/admin/src/translations/tr.json.mjs"], "sourcesContent": ["var Analytics = \"Analizler\";\nvar Documentation = \"Dokümantasyon\";\nvar Email = \"E-posta\";\nvar Password = \"Şifre\";\nvar Provider = \"Sağlayıcı\";\nvar ResetPasswordToken = \"Şifre sıfırlama anahtarı\";\nvar Role = \"Rol\";\nvar Username = \"Kullanıcı Adı\";\nvar Users = \"Kullanıcılar\";\nvar anErrorOccurred = \"Haydaa! Bir şeyler ters gitti. Lütfen tekrar dene.\";\nvar clearLabel = \"Temizle\";\nvar dark = \"Koyu\";\nvar light = \"Açık\";\nvar or = \"YA DA\";\nvar skipToContent = \"İçeriğe atla\";\nvar submit = \"Gönder\";\nvar tr = {\n    Analytics: Analytics,\n    \"Auth.components.Oops.text\": \"Hesabın donduruldu.\",\n    \"Auth.components.Oops.text.admin\": \"Hatalı olduğunu düşünüyorsanız lütfen yöneticinize ulaşın.\",\n    \"Auth.components.Oops.title\": \"Haydaa...\",\n    \"Auth.form.active.label\": \"Aktif\",\n    \"Auth.form.button.forgot-password\": \"E-posta gönder\",\n    \"Auth.form.button.go-home\": \"ANASAYFAYA GERİ DÖN\",\n    \"Auth.form.button.login\": \"Giriş\",\n    \"Auth.form.button.password-recovery\": \"Şifre Kurtarma\",\n    \"Auth.form.button.register\": \"Başlamaya hazır\",\n    \"Auth.form.email.label\": \"E-posta\",\n    \"Auth.form.email.placeholder\": \"<EMAIL>\",\n    \"Auth.form.error.blocked\": \"Hesabınız yönetici tarafından engellendi.\",\n    \"Auth.form.error.code.provide\": \"Geçersiz sağlanmış kod.\",\n    \"Auth.form.error.confirmed\": \"Tanımladığınız e-posta onaylanmadı.\",\n    \"Auth.form.error.email.invalid\": \"E-postası geçersiz.\",\n    \"Auth.form.error.email.provide\": \"Lütfen kullanıcı adınızı veya e-postanızı belirtin.\",\n    \"Auth.form.error.email.taken\": \"E-posta zaten alınmış\",\n    \"Auth.form.error.invalid\": \"Kimlik veya şifre geçersiz.\",\n    \"Auth.form.error.params.provide\": \"Geçersiz sağlanmış kod parametresi.\",\n    \"Auth.form.error.password.format\": \"Şifreniz `$` sembolünü üç kezden fazla içeremez.\",\n    \"Auth.form.error.password.local\": \"Bu kullanıcı hiçbir bir şifre belirlemedi; hesap oluşturma sırasında kullanılan sağlayıcı aracılığıyla lütfen giriş yapınız..\",\n    \"Auth.form.error.password.matching\": \"Parolalar uyuşmuyor.\",\n    \"Auth.form.error.password.provide\": \"Lütfen şifrenizi girin.\",\n    \"Auth.form.error.ratelimit\": \"Çok fazla deneme var. Lütfen bir dakika sonra tekrar deneyin.\",\n    \"Auth.form.error.user.not-exist\": \"Bu e-posta bulunmamaktadır..\",\n    \"Auth.form.error.username.taken\": \"Kullanıcı adı zaten alınmış\",\n    \"Auth.form.firstname.label\": \"Adın\",\n    \"Auth.form.firstname.placeholder\": \"ör. Zeynep\",\n    \"Auth.form.forgot-password.email.label\": \"E-postanızı giriniz\",\n    \"Auth.form.forgot-password.email.label.success\": \"E-posta başarıyla gönderildi, \",\n    \"Auth.form.lastname.label\": \"Soyadın\",\n    \"Auth.form.lastname.placeholder\": \"ör. Yılmaz\",\n    \"Auth.form.password.hide-password\": \"Şifreyi gizle\",\n    \"Auth.form.password.hint\": \"8 karakterden uzun olmalı ve en az 1 büyük harf, 1 küçük harf ve 1 sayı içermeli\",\n    \"Auth.form.password.show-password\": \"Şifreyi göster\",\n    \"Auth.form.register.news.label\": \"Beni gelecekteki özellikler ve geliştirmeler hakkında bilgilendir (bunu seçerek {terms} ve {policy}'leri kabul etmiş sayılırsınız)\",\n    \"Auth.form.register.subtitle\": \"Bilgiler yalnızca Strapi kimlik doğrulaması için kullanılacak. Tüm veriler sizin veritabanınızda saklanacak.\",\n    \"Auth.form.rememberMe.label\": \"Beni hatırla\",\n    \"Auth.form.username.label\": \"Kullanıcı Adı\",\n    \"Auth.form.username.placeholder\": \"Kai Doe\",\n    \"Auth.form.welcome.subtitle\": \"Strapi hesabına giriş yap\",\n    \"Auth.form.welcome.title\": \"Strapi'ye hoşgeldiniz!\",\n    \"Auth.link.forgot-password\": \"Parolanızı mı unuttunuz ?\",\n    \"Auth.link.ready\": \"Zaten kayıtlı mısınız?\",\n    \"Auth.link.signin\": \"Giriş yap\",\n    \"Auth.link.signin.account\": \"Hesabın var mı?\",\n    \"Auth.login.sso.divider\": \"Ya da bunlarla giriş yap\",\n    \"Auth.login.sso.loading\": \"Sağlayıcılar yükleniyor...\",\n    \"Auth.login.sso.subtitle\": \"Hesabına SSO ile giriş yap\",\n    \"Auth.privacy-policy-agreement.policy\": \"gizlilik sözleşmesi\",\n    \"Auth.privacy-policy-agreement.terms\": \"koşullar\",\n    \"Auth.reset-password.title\": \"Şifreni sıfırla\",\n    \"Content Manager\": \"İçerik Yönetimi\",\n    \"Content Type Builder\": \"İçerik-Tipi Kurucusu\",\n    Documentation: Documentation,\n    Email: Email,\n    \"Files Upload\": \"Dosya yükleme\",\n    \"HomePage.head.title\": \"Anasayfa\",\n    \"HomePage.roadmap\": \"Yol haritamızı görüntüleyin\",\n    \"HomePage.welcome.congrats\": \"Tebrikler!\",\n    \"HomePage.welcome.congrats.content\": \"İlk yönetici olarak giriş yaptınız. Strapi'nin güçlü özelliklerini keşfetmek için,\",\n    \"HomePage.welcome.congrats.content.bold\": \"ilk İçerik-Tipi'ni yaratmanızı öneriyoruz.\",\n    \"Media Library\": \"Ortam Kütüphanesi\",\n    \"New entry\": \"Yeni kayıt\",\n    Password: Password,\n    Provider: Provider,\n    ResetPasswordToken: ResetPasswordToken,\n    Role: Role,\n    \"Settings.PageTitle\": \"Ayarlar - {name}\",\n    \"Settings.tokens.Button.cancel\": \"İptal\",\n    \"Settings.tokens.Button.regenerate\": \"Yeniden üret\",\n    \"Settings.apiTokens.ListView.headers.createdAt\": \"Oluşturuldu\",\n    \"Settings.apiTokens.ListView.headers.description\": \"Tanım\",\n    \"Settings.apiTokens.ListView.headers.lastUsedAt\": \"En son kullanıldı\",\n    \"Settings.apiTokens.ListView.headers.name\": \"İsim\",\n    \"Settings.apiTokens.ListView.headers.type\": \"Token tipi\",\n    \"Settings.tokens.RegenerateDialog.title\": \"Tokenı yeniden üret.\",\n    \"Settings.apiTokens.addFirstToken\": \"İlk API Tokenınını ekle\",\n    \"Settings.apiTokens.addNewToken\": \"Yeni API Tokenı ekle\",\n    \"Settings.tokens.copy.editMessage\": \"Güvenlik sebebiyle, tokenı yalnızca bir kere görebilirsin.\",\n    \"Settings.tokens.copy.editTitle\": \"Bu tokena artık erişilemez.\",\n    \"Settings.tokens.copy.lastWarning\": \"Bu tokenı kopyalamayı unutma. Bir daha erişemeyeceksin!\",\n    \"Settings.apiTokens.create\": \"Yeni API Tokenı oluştur\",\n    \"Settings.apiTokens.createPage.permissions.description\": \"Sadece bir yol ile bağlanmış eylemler listelenmektedir.\",\n    \"Settings.apiTokens.createPage.permissions.title\": \"İzinler\",\n    \"Settings.apiTokens.description\": \"API'ı kullanmak için oluşturulmuş token listesi\",\n    \"Settings.tokens.duration.30-days\": \"30 gün\",\n    \"Settings.tokens.duration.7-days\": \"7 gün\",\n    \"Settings.tokens.duration.90-days\": \"90 gün\",\n    \"Settings.tokens.duration.expiration-date\": \"Sona erme tarihi\",\n    \"Settings.tokens.duration.unlimited\": \"Sınırsız\",\n    \"Settings.apiTokens.emptyStateLayout\": \"Henüz hiç içeriğin yok...\",\n    \"Settings.tokens.form.duration\": \"Token süresi\",\n    \"Settings.tokens.form.type\": \"Token tipi\",\n    \"Settings.tokens.notification.copied\": \"Token panoya kopyalandı.\",\n    \"Settings.tokens.popUpWarning.message\": \"Bu tokenı yeniden üretmek istediğinden emin misin?\",\n    \"Settings.apiTokens.title\": \"API Tokenları\",\n    \"Settings.tokens.types.full-access\": \"Tam yetki\",\n    \"Settings.tokens.types.read-only\": \"Salt-okunur\",\n    \"Settings.application.customization\": \"Özelleştirme\",\n    \"Settings.application.customization.carousel-hint\": \"Yönetim paneli logosunu değiştir. (Görsel boyutu sınırı: {dimension}x{dimension}, Dosya boyutu sınırı: {size}KB)\",\n    \"Settings.application.customization.carousel-slide.label\": \"Logo slaytı\",\n    \"Settings.application.customization.carousel.change-action\": \"Logoyu değiştir\",\n    \"Settings.application.customization.carousel.reset-action\": \"Logoyu sıfırla\",\n    \"Settings.application.customization.carousel.title\": \"Logo\",\n    \"Settings.application.customization.modal.cancel\": \"İptal\",\n    \"Settings.application.customization.modal.pending\": \"Bekleyen logo\",\n    \"Settings.application.customization.modal.pending.card-badge\": \"görsel\",\n    \"Settings.application.customization.modal.pending.choose-another\": \"Başka bir logo seç\",\n    \"Settings.application.customization.modal.pending.subtitle\": \"Yüklemeden önce seçilen logoyu yönet\",\n    \"Settings.application.customization.modal.pending.title\": \"Logo yüklemeye hazır\",\n    \"Settings.application.customization.modal.pending.upload\": \"Logo yükle\",\n    \"Settings.application.customization.modal.tab.label\": \"Dosyaları nasıl yüklemek istersin?\",\n    \"Settings.application.customization.modal.upload\": \"Logo yükle\",\n    \"Settings.application.customization.modal.upload.cta.browse\": \"Dosyalara gözat\",\n    \"Settings.application.customization.modal.upload.drag-drop\": \"Buraya sürükle bırak ya da\",\n    \"Settings.application.customization.modal.upload.error-format\": \"Desteklenmeyen biçim algılandı (desteklenen biçimler: jpeg, jpg, png, svg).\",\n    \"Settings.application.customization.modal.upload.error-network\": \"Ağ hatası\",\n    \"Settings.application.customization.modal.upload.error-size\": \"Yüklenen dosya çok büyük (Görsel boyutu sınırı: {dimension}x{dimension}, Dosya boyutu sınırı: {size}KB)\",\n    \"Settings.application.customization.modal.upload.file-validation\": \"Görsel boyutu sınırı: {dimension}x{dimension}, Dosya boyutu sınırı: {size}KB\",\n    \"Settings.application.customization.modal.upload.from-computer\": \"Bilgisayarımdan\",\n    \"Settings.application.customization.modal.upload.from-url\": \"URLden\",\n    \"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n    \"Settings.application.customization.modal.upload.next\": \"İleri\",\n    \"Settings.application.description\": \"Yönetim panelinin tüm bilgileri\",\n    \"Settings.application.edition-title\": \"mevcut sürüm\",\n    \"Settings.application.get-help\": \"Yardım al\",\n    \"Settings.application.link-pricing\": \"Tüm ücret planlarını gör\",\n    \"Settings.application.link-upgrade\": \"Admin panelini yükselt\",\n    \"Settings.application.node-version\": \"node versiyonu\",\n    \"Settings.application.strapi-version\": \"strapi versiyonu\",\n    \"Settings.application.strapiVersion\": \"strapi versiyonu\",\n    \"Settings.application.title\": \"Kuşbakışı\",\n    \"Settings.error\": \"Hata\",\n    \"Settings.global\": \"Genel Ayarlar\",\n    \"Settings.permissions\": \"Yönetim paneli\",\n    \"Settings.permissions.category\": \"{category} için izin ayarları\",\n    \"Settings.permissions.category.plugins\": \"{category} eklentisi için izin ayarları\",\n    \"Settings.permissions.conditions.anytime\": \"Her zaman\",\n    \"Settings.permissions.conditions.apply\": \"Uygula\",\n    \"Settings.permissions.conditions.can\": \"Yapabilir\",\n    \"Settings.permissions.conditions.conditions\": \"Koşullar\",\n    \"Settings.permissions.conditions.links\": \"Bağlantılar\",\n    \"Settings.permissions.conditions.no-actions\": \"Koşulları belirtmeden önce eylemleri (oluştur, oku, güncelle, ...) seçmelisin.\",\n    \"Settings.permissions.conditions.none-selected\": \"Her zaman\",\n    \"Settings.permissions.conditions.or\": \"YA DA\",\n    \"Settings.permissions.conditions.when\": \"Olduğunda\",\n    \"Settings.permissions.select-all-by-permission\": \"Tüm {label} izinlerini seç\",\n    \"Settings.permissions.select-by-permission\": \"{label} iznini seç\",\n    \"Settings.permissions.users.active\": \"Aktif\",\n    \"Settings.permissions.users.create\": \"Kullanıcı davet et\",\n    \"Settings.permissions.users.email\": \"E-Posta\",\n    \"Settings.permissions.users.firstname\": \"Adı\",\n    \"Settings.permissions.users.form.sso\": \"SSO ile bağlan\",\n    \"Settings.permissions.users.form.sso.description\": \"Açıldığında kullanıcılar SSO ile giriş yapabilir\",\n    \"Settings.permissions.users.inactive\": \"Pasif\",\n    \"Settings.permissions.users.lastname\": \"Soyadı\",\n    \"Settings.permissions.users.listview.header.subtitle\": \"Strapi yönetim paneline erişimi olan kullanıcılar\",\n    \"Settings.permissions.users.roles\": \"Roller\",\n    \"Settings.permissions.users.strapi-author\": \"Yazar\",\n    \"Settings.permissions.users.strapi-editor\": \"Editör\",\n    \"Settings.permissions.users.strapi-super-admin\": \"Süper Yönetici\",\n    \"Settings.permissions.users.tabs.label\": \"Sekme İzinleri\",\n    \"Settings.permissions.users.user-status\": \"Kullanıcı durumu\",\n    \"Settings.permissions.users.username\": \"Kullanıcı adı\",\n    \"Settings.profile.form.notify.data.loaded\": \"Profil verilerin yüklendi\",\n    \"Settings.profile.form.section.experience.clear.select\": \"Seçilmiş arayüz dilini temizle\",\n    \"Settings.profile.form.section.experience.here\": \"buradan\",\n    \"Settings.profile.form.section.experience.interfaceLanguage\": \"Arayüz dili\",\n    \"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Bu yalnızca senin arayüzünü seçilen dilde gösterecek.\",\n    \"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Tercih değişiklikleri yalnızca sana uygulanır. Daha fazla bilgi için {here}.\",\n    \"Settings.profile.form.section.experience.mode.hint\": \"Arayüzünü seçilen modda gösterir.\",\n    \"Settings.profile.form.section.experience.mode.label\": \"Arayüz modu\",\n    \"Settings.profile.form.section.experience.mode.option-label\": \"{name} modu\",\n    \"Settings.profile.form.section.experience.title\": \"Deneyim\",\n    \"Settings.profile.form.section.head.title\": \"Kullanıcı profili\",\n    \"Settings.profile.form.section.profile.page.title\": \"Profil sayfası\",\n    \"Settings.roles.create.description\": \"Role verilen hakları tanımla\",\n    \"Settings.roles.create.title\": \"Rol oluştur\",\n    \"Settings.roles.created\": \"Rol oluşturuldu\",\n    \"Settings.roles.edit.title\": \"Rolü düzenle\",\n    \"Settings.roles.form.button.users-with-role\": \"Bu rolde {number} kullanıcı\",\n    \"Settings.roles.form.created\": \"Oluşturuldu\",\n    \"Settings.roles.form.description\": \"Rolün adı ve tanımı\",\n    \"Settings.roles.form.permission.property-label\": \"{label} izinleri\",\n    \"Settings.roles.form.permissions.attributesPermissions\": \"Alanların izinleri\",\n    \"Settings.roles.form.permissions.create\": \"Oluştur\",\n    \"Settings.roles.form.permissions.delete\": \"Sil\",\n    \"Settings.roles.form.permissions.publish\": \"Yayınla\",\n    \"Settings.roles.form.permissions.read\": \"Oku\",\n    \"Settings.roles.form.permissions.update\": \"Güncelle\",\n    \"Settings.roles.list.button.add\": \"Yeni rol ekle\",\n    \"Settings.roles.list.description\": \"Rollerin listesi\",\n    \"Settings.roles.title.singular\": \"rol\",\n    \"Settings.sso.description\": \"Single Sign-On (SSO) özelliğini ayarla.\",\n    \"Settings.sso.form.registration.description\": \"SSO girişi sırasında hesabı olmayanlara yeni hesap oluştur\",\n    \"Settings.sso.form.registration.label\": \"Otomatik kayıt\",\n    \"Settings.sso.title\": \"Single Sign-On\",\n    \"Settings.webhooks.create\": \"Webhook oluştur\",\n    \"Settings.webhooks.create.header\": \"Yeni başlık yarat\",\n    \"Settings.webhooks.created\": \"Webhook oluşturuldu\",\n    \"Settings.webhooks.event.publish-tooltip\": \"Bu eylem yalnızca Taslak/Yayımla sistemi açık olduğunda vardır\",\n    \"Settings.webhooks.events.create\": \"Oluştur\",\n    \"Settings.webhooks.events.update\": \"Güncelle\",\n    \"Settings.webhooks.form.events\": \"Etkinlikler\",\n    \"Settings.webhooks.form.headers\": \"Başlıklar\",\n    \"Settings.webhooks.form.url\": \"Url\",\n    \"Settings.webhooks.headers.remove\": \"{number} başlık satırını kaldır\",\n    \"Settings.webhooks.key\": \"Anahtar\",\n    \"Settings.webhooks.list.button.add\": \"Yeni webhook ekle\",\n    \"Settings.webhooks.list.description\": \"POST değişiklikleri bildirimi al.\",\n    \"Settings.webhooks.list.empty.description\": \"İlkini bu listeye ekleyin.\",\n    \"Settings.webhooks.list.empty.link\": \"Dökümantasyonumuzu görüntüleyin\",\n    \"Settings.webhooks.list.empty.title\": \"Henüz bir webhook yok\",\n    \"Settings.webhooks.list.th.actions\": \"eylemler\",\n    \"Settings.webhooks.list.th.status\": \"durum\",\n    \"Settings.webhooks.singular\": \"webhook\",\n    \"Settings.webhooks.title\": \"Webhooklar\",\n    \"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength} dosya seçildi\",\n    \"Settings.webhooks.trigger\": \"Tetikleyici\",\n    \"Settings.webhooks.trigger.cancel\": \"Tetikleyiciyi iptal et\",\n    \"Settings.webhooks.trigger.pending\": \"Bekleniyor...\",\n    \"Settings.webhooks.trigger.save\": \"Lütfen tetikleyiciyi kaydedin\",\n    \"Settings.webhooks.trigger.success\": \"Başarılı!\",\n    \"Settings.webhooks.trigger.success.label\": \"Tetikleyici başarılı\",\n    \"Settings.webhooks.trigger.test\": \"Test-tetikleyici\",\n    \"Settings.webhooks.trigger.title\": \"Tetikleyiciden önce kaydet\",\n    \"Settings.webhooks.value\": \"Değer\",\n    \"Usecase.back-end\": \"Arkayüz Geliştiricisi\",\n    \"Usecase.button.skip\": \"Bu soruyu atla\",\n    \"Usecase.content-creator\": \"İçerik Üreticisi\",\n    \"Usecase.front-end\": \"Önyüz Geliştiricisi\",\n    \"Usecase.full-stack\": \"Tümyüz Geliştiricisi\",\n    \"Usecase.input.work-type\": \"Ne işle meşgulsun?\",\n    \"Usecase.notification.success.project-created\": \"Proje başarıyla oluşturuldu\",\n    \"Usecase.other\": \"Diğer\",\n    \"Usecase.title\": \"Biraz kendinden bahset\",\n    Username: Username,\n    Users: Users,\n    \"Users & Permissions\": \"Kullanıcılar & İzinler\",\n    \"admin.pages.MarketPlacePage.filters.categories\": \"Kategoriler\",\n    \"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"{count} kategori seçildi\",\n    \"admin.pages.MarketPlacePage.filters.collections\": \"Koleksiyonlar\",\n    \"admin.pages.MarketPlacePage.filters.collectionsSelected\": \"{count} koleksiyon seçildi\",\n    \"admin.pages.MarketPlacePage.head\": \"Pazaryeri - Eklentiler\",\n    \"admin.pages.MarketPlacePage.missingPlugin.description\": \"Bize aradığın eklentiyi anlat ki biz de eklenti geliştirici topluluğumuzdaki ilham arayanlara iletelim!\",\n    \"admin.pages.MarketPlacePage.missingPlugin.title\": \"Aradığın eklentiyi bulamadın mı?\",\n    \"admin.pages.MarketPlacePage.offline.subtitle\": \"Strapi Market'e erişmek için Internet'e bağlı olmalısın.\",\n    \"admin.pages.MarketPlacePage.offline.title\": \"Çevrimdışısın\",\n    \"admin.pages.MarketPlacePage.plugin.copy\": \"Yükleme komutunu kopyala\",\n    \"admin.pages.MarketPlacePage.plugin.copy.success\": \"Yükleme komutu terminaline yapıştırılmak için hazır\",\n    \"admin.pages.MarketPlacePage.plugin.downloads\": \"Bu eklenti haftada {downloadsCount} kez indirilmiş\",\n    \"admin.pages.MarketPlacePage.plugin.githubStars\": \"Bu eklenti Github'da {starsCount} yıldız almış\",\n    \"admin.pages.MarketPlacePage.plugin.info\": \"Daha fazla\",\n    \"admin.pages.MarketPlacePage.plugin.info.label\": \"{pluginName} hakkında daha fazla öğren\",\n    \"admin.pages.MarketPlacePage.plugin.info.text\": \"Daha\",\n    \"admin.pages.MarketPlacePage.plugin.installed\": \"Yüklendi\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Strapi tarafından geliştirildi\",\n    \"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Eklenti Strapi tarafından onaylandı\",\n    \"admin.pages.MarketPlacePage.plugin.version\": \"Strapi'yi \\\"{strapiAppVersion}\\\" versiyonundan \\\"{versionRange}\\\" versiyonuna yükselt\",\n    \"admin.pages.MarketPlacePage.plugin.version.null\": \"Yüklü olan \\\"{strapiAppVersion}\\\" Strapi versiyonu ile uyumluluğu doğrulanamıyor\",\n    \"admin.pages.MarketPlacePage.plugins\": \"Eklentiler\",\n    \"admin.pages.MarketPlacePage.provider.downloads\": \"Bu sağlayıcı haftada {downloadsCount} kez indirilmiş\",\n    \"admin.pages.MarketPlacePage.provider.githubStars\": \"Bu sağlayıcı Github'da {starsCount} yıldız almış\",\n    \"admin.pages.MarketPlacePage.providers\": \"Sağlayıcılar\",\n    \"admin.pages.MarketPlacePage.search.clear\": \"Aramayı temizle\",\n    \"admin.pages.MarketPlacePage.search.empty\": \"\\\"{target}\\\" için sonuç yok\",\n    \"admin.pages.MarketPlacePage.search.placeholder\": \"Arama\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical\": \"Alfabetik sıraya diz\",\n    \"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"Alfabetik sıra\",\n    \"admin.pages.MarketPlacePage.sort.newest\": \"Yeniden eskiye diz\",\n    \"admin.pages.MarketPlacePage.sort.newest.selected\": \"Yeniden eskiye\",\n    \"admin.pages.MarketPlacePage.submit.plugin.link\": \"Eklenti gönder\",\n    \"admin.pages.MarketPlacePage.submit.provider.link\": \"Sağlayıcı gönder\",\n    \"admin.pages.MarketPlacePage.subtitle\": \"Strapi'den daha fazlasını al\",\n    \"admin.pages.MarketPlacePage.tab-group.label\": \"Strapi Eklenti ve Sağlayıcıları\",\n    anErrorOccurred: anErrorOccurred,\n    \"app.component.CopyToClipboard.label\": \"Panoya kopyala\",\n    \"app.component.search.label\": \"{target} için arama yap\",\n    \"app.component.table.duplicate\": \"{target} kaydını yinele\",\n    \"app.component.table.edit\": \"{target} kaydını düzenle\",\n    \"app.component.table.select.one-entry\": \"{target} kaydını seç\",\n    \"app.components.BlockLink.blog\": \"Blog\",\n    \"app.components.BlockLink.blog.content\": \"Strapi ve ekosistemi hakkındaki son haberleri oku.\",\n    \"app.components.BlockLink.code\": \"Kod örnekleri\",\n    \"app.components.BlockLink.documentation.content\": \"Başlıca konseptleri, rehberleri ve talimatları keşfet.\",\n    \"app.components.BlockLink.tutorial\": \"Eğitimler\",\n    \"app.components.BlockLink.tutorial.content\": \"Strapi'yi kullanmak ve özelleştirmek için adım adım talimatları takip et.\",\n    \"app.components.Button.cancel\": \"İptal\",\n    \"app.components.Button.confirm\": \"Onayla\",\n    \"app.components.Button.reset\": \"Sıfırla\",\n    \"app.components.ComingSoonPage.comingSoon\": \"Çok Yakında\",\n    \"app.components.ConfirmDialog.title\": \"Onay\",\n    \"app.components.DownloadInfo.download\": \"İndirme devam ediyor...\",\n    \"app.components.DownloadInfo.text\": \"Bu birkaç dakika sürebilir. Sabrınız için teşekkürler.\",\n    \"app.components.EmptyAttributes.title\": \"Alan henüz yok\",\n    \"app.components.EmptyStateLayout.content-document\": \"İçerik bulunamadı\",\n    \"app.components.EmptyStateLayout.content-permissions\": \"Bu içeriğe erişim yetkiniz yok.\",\n    \"app.components.GuidedTour.CM.create.content\": \"<p>Buradaki tüm içerikleri İçerik Yöneticisi ile oluştur ve yönet.</p><p>Ör: Blog websitesi örneğini bir adım daha öteye götürürsek, kişiler burada istedikleri gibi Makale yazabilir, kaydedip yayımlayabilir.</p><p>💡 Bir ipucu - Oluşturduğun içeriklerde yayınla butonuna basmayı unutma.</p>\",\n    \"app.components.GuidedTour.CM.create.title\": \"⚡️ İçerik oluştur\",\n    \"app.components.GuidedTour.CM.success.content\": \"<p>Müthiş! Son bir adım kaldı.</p><b>🚀 İçeriği çalışırken gör</b>\",\n    \"app.components.GuidedTour.CM.success.cta.title\": \"APIyi test et\",\n    \"app.components.GuidedTour.CM.success.title\": \"Adım 2: Tamamlandı ✅\",\n    \"app.components.GuidedTour.CTB.create.content\": \"<p>Koleksiyon tipleri birden çok girdiyi yönetmene yardımcı olur. Tekil tipler tek bir girdiyi yönetmek için uygundur.</p> <p>Ör: Bir blog sayfası için, Makaleler Koleksiyon tipinde olabilecekken, Ana Sayfa Tekil tipte olacaktır.</p>\",\n    \"app.components.GuidedTour.CTB.create.cta.title\": \"Bir Koleksiyon tipi kur\",\n    \"app.components.GuidedTour.CTB.create.title\": \"🧠 İlk Koleksiyon tipini oluştur\",\n    \"app.components.GuidedTour.CTB.success.content\": \"<p>İyi gidiyorsun!</p><b>⚡️ Dünya ile ne paylaşmak isterdin?</b>\",\n    \"app.components.GuidedTour.CTB.success.title\": \"Adım 1: Tamamlandı ✅\",\n    \"app.components.GuidedTour.apiTokens.create.content\": \"<p>Bir kimlik doğrulama tokenı üret ve yeni oluşturduğun içeriğe ulaş.</p>\",\n    \"app.components.GuidedTour.apiTokens.create.cta.title\": \"Bir API Token üret\",\n    \"app.components.GuidedTour.apiTokens.create.title\": \"🚀 İçeriği çalışırken gör\",\n    \"app.components.GuidedTour.apiTokens.success.content\": \"<p>Bir HTTP isteiği yaparak içeriği çalışırlen gör:</p><ul><li><p>URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>With the header: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>İçeriklerle etkileşimin farklı yöntemler için <documentationLink>dokümantasyonu</documentationLink> oku.</p>\",\n    \"app.components.GuidedTour.apiTokens.success.cta.title\": \"Ana sayfaya geri dön\",\n    \"app.components.GuidedTour.apiTokens.success.title\": \"Adım 3: Tamamlandı ✅\",\n    \"app.components.GuidedTour.create-content\": \"İçerik oluştur\",\n    \"app.components.GuidedTour.home.CM.title\": \"⚡️ Dünya ile ne paylaşmak isterdin?\",\n    \"app.components.GuidedTour.home.CTB.cta.title\": \"İçerik tipi kurucusuna git\",\n    \"app.components.GuidedTour.home.CTB.title\": \"🧠 İçerik yapısını kur\",\n    \"app.components.GuidedTour.home.apiTokens.cta.title\": \"APIyi test et\",\n    \"app.components.GuidedTour.skip\": \"Turu atla\",\n    \"app.components.GuidedTour.title\": \"Başlamak için 3 adım\",\n    \"app.components.HomePage.button.blog\": \"BLOG SAYFASINDA DAHA FAZLASINI GÖRÜN\",\n    \"app.components.HomePage.community\": \"Topluluğumuza ulaşın\",\n    \"app.components.HomePage.community.content\": \"Farklı kanallarda takım üyeleri, katkıda bulunanlar ve geliştiricilere ulaşın.\",\n    \"app.components.HomePage.create\": \"İlk içerik tipini oluştur\",\n    \"app.components.HomePage.welcome\": \"Panele hoşgeldiniz.\",\n    \"app.components.HomePage.welcome.again\": \"Hoşgeldiniz \",\n    \"app.components.HomePage.welcomeBlock.content\": \"Sizi topluluk üyelerinden biri olarak görmekten mutluyuz. Sürekli olarak geri bildirim alabilmemiz için bize doğrudan mesaj göndermeye çekinmeyin \",\n    \"app.components.HomePage.welcomeBlock.content.again\": \"Projenizde ilerleme kaydedeceğinizi umuyoruz... Strapi ile ilgili en yeni yenilikleri okumaktan çekinmeyin. Ürünü geri bildirimlerinize göre geliştirmek için elimizden geleni yapıyoruz.\",\n    \"app.components.HomePage.welcomeBlock.content.issues\": \"sorunlar\",\n    \"app.components.HomePage.welcomeBlock.content.raise\": \" yada yükselt \",\n    \"app.components.ImgPreview.hint\": \"Dosyanızı bu alana sürükleyip bırakın ya da bir dosya yüklemek için {browse}\",\n    \"app.components.ImgPreview.hint.browse\": \"gözat\",\n    \"app.components.InputFile.newFile\": \"Yeni dosya ekle\",\n    \"app.components.InputFileDetails.open\": \"Yeni sekmede aç\",\n    \"app.components.InputFileDetails.originalName\": \"Orjinal isim:\",\n    \"app.components.InputFileDetails.remove\": \"Bu dosyayı sil\",\n    \"app.components.InputFileDetails.size\": \"Boyut:\",\n    \"app.components.InstallPluginPage.Download.description\": \"Eklentiyi indirmek ve yüklemek bir kaç saniye sürebilir.\",\n    \"app.components.InstallPluginPage.Download.title\": \"İndiriliyor...\",\n    \"app.components.InstallPluginPage.description\": \"Uygulamanızı rahatlıkla genişletin.\",\n    \"app.components.LeftMenu.collapse\": \"Menüyü ufalt\",\n    \"app.components.LeftMenu.expand\": \"Menüyü büyüt\",\n    \"app.components.LeftMenu.general\": \"Genel\",\n    \"app.components.LeftMenu.logo.alt\": \"Uygulama logosu\",\n    \"app.components.LeftMenu.logout\": \"Çıkış\",\n    \"app.components.LeftMenu.navbrand.title\": \"Strapi Panosu\",\n    \"app.components.LeftMenu.navbrand.workplace\": \"İş Yeri\",\n    \"app.components.LeftMenu.plugins\": \"Eklentiler\",\n    \"app.components.LeftMenu.trialCountdown\": \"Sizin deneme süreniz {date} günü sona erecektir.\",\n    \"app.components.LeftMenuFooter.help\": \"Yardım\",\n    \"app.components.LeftMenuFooter.poweredBy\": \"Gururla sunar \",\n    \"app.components.LeftMenuLinkContainer.collectionTypes\": \"Koleksiyon Tipleri\",\n    \"app.components.LeftMenuLinkContainer.configuration\": \"Yapılandırma\",\n    \"app.components.LeftMenuLinkContainer.general\": \"Genel\",\n    \"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Yüklenen eklenti bulunmamaktadır.\",\n    \"app.components.LeftMenuLinkContainer.plugins\": \"Eklentiler\",\n    \"app.components.LeftMenuLinkContainer.singleTypes\": \"Tekil Tipler\",\n    \"app.components.ListPluginsPage.deletePlugin.description\": \"Eklentiyi kaldırmak bir kaç saniye alabilir.\",\n    \"app.components.ListPluginsPage.deletePlugin.title\": \"Kaldırılıyor\",\n    \"app.components.ListPluginsPage.description\": \"Projedeki yüklenen eklentiler.\",\n    \"app.components.ListPluginsPage.head.title\": \"Eklenti Listesi\",\n    \"app.components.Logout.logout\": \"Çıkış Yap\",\n    \"app.components.Logout.profile\": \"Profil\",\n    \"app.components.MarketplaceBanner\": \"Strapi Awesome'da projeni hayata geçirmek için harika şeyleri ve topluluk tarafından geliştirilmiş eklentileri keşfet.\",\n    \"app.components.MarketplaceBanner.image.alt\": \"bir strapi roket logosu\",\n    \"app.components.MarketplaceBanner.link\": \"Şimdi gözden geçir\",\n    \"app.components.NotFoundPage.back\": \"Anasayfaya geri dön\",\n    \"app.components.NotFoundPage.description\": \"Bulunamadı\",\n    \"app.components.Official\": \"Resmi\",\n    \"app.components.Onboarding.help.button\": \"Yardım butonu\",\n    \"app.components.Onboarding.label.completed\": \"% tamamlandı\",\n    \"app.components.Onboarding.title\": \"Başlangıç Videolaro\",\n    \"app.components.PluginCard.Button.label.download\": \"İndir\",\n    \"app.components.PluginCard.Button.label.install\": \"Zaten yüklenmiş\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"autoReload özelliği aktif edilmeli. Lütfen uygulamayı `yarn develop` ile başlatın.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Anladım!\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Güvenlik nedeniyle bir eklenti yalnızca geliştirme ortamında indirilebilir.\",\n    \"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"İndirme imkansız\",\n    \"app.components.PluginCard.compatible\": \"Uygulamanızla uyumlu\",\n    \"app.components.PluginCard.compatibleCommunity\": \"Toplulukla uyumlu\",\n    \"app.components.PluginCard.more-details\": \"Daha fazla detay\",\n    \"app.components.ToggleCheckbox.off-label\": \"Yanlış\",\n    \"app.components.ToggleCheckbox.on-label\": \"Doğru\",\n    \"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Bir kullanıcı bir ya da daha fazla role sahip olabilir\",\n    \"app.components.listPlugins.button\": \"Yeni eklenti ekle\",\n    \"app.components.listPlugins.title.none\": \"Yüklenen eklenti bulunmamaktadır.\",\n    \"app.components.listPluginsPage.deletePlugin.error\": \"Eklenti kaldırılırken bir hata oluştu\",\n    \"app.containers.App.notification.error.init\": \"API isteği sırasında bir hata oluştu\",\n    \"app.links.configure-view\": \"Ekranı düzenle\",\n    \"app.page.not.found\": \"Haydaa! Aradığın sayfayı bulamıyor gibiyiz...\",\n    \"app.static.links.cheatsheet\": \"ÖzetYardım\",\n    \"app.utils.SelectOption.defaultMessage\": \" \",\n    \"app.utils.add-filter\": \"Filtre ekle\",\n    \"app.utils.close-label\": \"Kapat\",\n    \"app.utils.defaultMessage\": \" \",\n    \"app.utils.delete\": \"Sil\",\n    \"app.utils.duplicate\": \"Yinele\",\n    \"app.utils.edit\": \"Düzenle\",\n    \"app.utils.errors.file-too-big.message\": \"Dosya çok büyük\",\n    \"app.utils.filter-value\": \"Değeri filtrele\",\n    \"app.utils.filters\": \"Filtreler\",\n    \"app.utils.notify.data-loaded\": \"{target} yüklendi\",\n    \"app.utils.placeholder.defaultMessage\": \" \",\n    \"app.utils.publish\": \"Yayınla\",\n    \"app.utils.select-all\": \"Tümünü seç\",\n    \"app.utils.select-field\": \"Alanı seç\",\n    \"app.utils.select-filter\": \"Filtreyi seç\",\n    \"app.utils.unpublish\": \"Yayından Kaldır\",\n    clearLabel: clearLabel,\n    \"coming.soon\": \"Bu içerik şuanda düzenleniyor. Bir kaç hafta sonra yayında olacak!\",\n    \"component.Input.error.validation.integer\": \"Değer sayı olmalı\",\n    \"components.AutoReloadBlocker.description\": \"Strapi'yi aşağıdaki komutlardan biri ile çalıştırın:\",\n    \"components.AutoReloadBlocker.header\": \"Bu eklenti için tekrar yükleme özelliği gerekiyor.\",\n    \"components.ErrorBoundary.title\": \"Bir şeyler yanlış gitti...\",\n    \"components.FilterOptions.FILTER_TYPES.$contains\": \"içerir\",\n    \"components.FilterOptions.FILTER_TYPES.$containsi\": \"içerir (büyük/küçük harfe duyarsız)\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWith\": \"ile biter\",\n    \"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"ile biter (büyük/küçük harfe duyarsız)\",\n    \"components.FilterOptions.FILTER_TYPES.$eq\": \"eşittir\",\n    \"components.FilterOptions.FILTER_TYPES.$eqi\": \"eşittir (büyük/küçük harfe duyarsız)\",\n    \"components.FilterOptions.FILTER_TYPES.$gt\": \"büyüktür\",\n    \"components.FilterOptions.FILTER_TYPES.$gte\": \"büyük eşittir\",\n    \"components.FilterOptions.FILTER_TYPES.$lt\": \"küçüktür\",\n    \"components.FilterOptions.FILTER_TYPES.$lte\": \"küçük eşittir\",\n    \"components.FilterOptions.FILTER_TYPES.$ne\": \"eşit değildir\",\n    \"components.FilterOptions.FILTER_TYPES.$nei\": \"eşit değildir (büyük/küçük harfe duyarsız)\",\n    \"components.FilterOptions.FILTER_TYPES.$notContains\": \"içermez\",\n    \"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"içermez (büyük/küçük harfe duyarsız)\",\n    \"components.FilterOptions.FILTER_TYPES.$notNull\": \"null değildir\",\n    \"components.FilterOptions.FILTER_TYPES.$null\": \"null'dur\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWith\": \"başlar\",\n    \"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"başlar (büyük/küçük harfe duyarsız)\",\n    \"components.Input.error.attribute.key.taken\": \"Bu değer zaten var.\",\n    \"components.Input.error.attribute.sameKeyAndName\": \"Eşit olamaz\",\n    \"components.Input.error.attribute.taken\": \"Bu alan ismi zaten var.\",\n    \"components.Input.error.contain.lowercase\": \"Şifre en az bir küçük harf içermelidir\",\n    \"components.Input.error.contain.number\": \"Şifre en az bir sayı içermelidir\",\n    \"components.Input.error.contain.uppercase\": \"Şifre en az bir büyük harf içermelidir\",\n    \"components.Input.error.contentTypeName.taken\": \"Bu isim zaten var.\",\n    \"components.Input.error.custom-error\": \"{errorMessage} \",\n    \"components.Input.error.password.noMatch\": \"Şifreler uyuşmuyor\",\n    \"components.Input.error.validation.email\": \"Geçersiz e-posta adresi.\",\n    \"components.Input.error.validation.json\": \"Bu JSON biçimi ile eşleşmiyor\",\n    \"components.Input.error.validation.lowercase\": \"Değerin tamamı küçük harf olmalıdır\",\n    \"components.Input.error.validation.max\": \"Değer çok yüksek {max}.\",\n    \"components.Input.error.validation.maxLength\": \"Değer çok uzun {max}.\",\n    \"components.Input.error.validation.min\": \"Değer çok az {min}.\",\n    \"components.Input.error.validation.minLength\": \"Değer çok kısa {min}.\",\n    \"components.Input.error.validation.minSupMax\": \"Üstü olamaz\",\n    \"components.Input.error.validation.regex\": \"Regex ile eşleşmiyor.\",\n    \"components.Input.error.validation.required\": \"Zorunlu alandır.\",\n    \"components.Input.error.validation.unique\": \"Değer zaten kullanılmış.\",\n    \"components.InputSelect.option.placeholder\": \"Buradan seçin\",\n    \"components.ListRow.empty\": \"Gösterilecek veri bulunmamaktadır.\",\n    \"components.NotAllowedInput.text\": \"Bu alanı görmek için yetkin yok\",\n    \"components.OverlayBlocker.description\": \"Sunucunun yeniden başlatılması gereken bir özellik kullanıyorsunuz. Lütfen sunucu çalışana kadar bekleyin.\",\n    \"components.OverlayBlocker.description.serverError\": \"Sunucu yeniden başlatılmalı, lütfen terminal üzerinden logları kontrol edin.\",\n    \"components.OverlayBlocker.title\": \"Yeniden başlatılmayı bekliyor...\",\n    \"components.OverlayBlocker.title.serverError\": \"Yeniden başlatma beklendiğinden uzun sürüyor\",\n    \"components.PageFooter.select\": \"sayfa başına kayıt\",\n    \"components.ProductionBlocker.description\": \"Güvenlik nedeniyle, bu eklentiyi diğer ortamlarda devre dışı bırakmamız gerekir.\",\n    \"components.ProductionBlocker.header\": \"Bu eklenti yalnızca geliştirme aşamasında mevcuttur.\",\n    \"components.Search.placeholder\": \"Arama...\",\n    \"components.TableHeader.sort\": \"Şuna göre diz: {label}\",\n    \"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown modu\",\n    \"components.Wysiwyg.ToggleMode.preview-mode\": \"Önizleme modu\",\n    \"components.Wysiwyg.collapse\": \"Daralt\",\n    \"components.Wysiwyg.selectOptions.H1\": \"H1 başlık\",\n    \"components.Wysiwyg.selectOptions.H2\": \"H2 başlık\",\n    \"components.Wysiwyg.selectOptions.H3\": \"H3 başlık\",\n    \"components.Wysiwyg.selectOptions.H4\": \"H4 başlık\",\n    \"components.Wysiwyg.selectOptions.H5\": \"H5 başlık\",\n    \"components.Wysiwyg.selectOptions.H6\": \"H6 başlık\",\n    \"components.Wysiwyg.selectOptions.title\": \"Başlık ekle\",\n    \"components.WysiwygBottomControls.charactersIndicators\": \"karakter\",\n    \"components.WysiwygBottomControls.fullscreen\": \"Genişlet\",\n    \"components.WysiwygBottomControls.uploadFiles\": \"Dosyanızı bu alana sürükleyip bırakın ya da bir dosya yüklemek için {browse}\",\n    \"components.WysiwygBottomControls.uploadFiles.browse\": \"Bunları seç\",\n    \"components.pagination.go-to\": \"{page} nolu sayfaya git\",\n    \"components.pagination.go-to-next\": \"Sonraki sayfaya git\",\n    \"components.pagination.go-to-previous\": \"Önceki sayfaya git\",\n    \"components.pagination.remaining-links\": \"Ve {number} diğer bağlantı\",\n    \"components.popUpWarning.button.cancel\": \"Hayır, iptal et\",\n    \"components.popUpWarning.button.confirm\": \"Evet, onayla\",\n    \"components.popUpWarning.message\": \"Bunu silmek istediğinizden emin misiniz?\",\n    \"components.popUpWarning.title\": \"Lütfen onaylayın\",\n    dark: dark,\n    \"form.button.continue\": \"Devam\",\n    \"form.button.done\": \"Tamam\",\n    \"global.actions\": \"Eylemler\",\n    \"global.back\": \"Geri\",\n    \"global.cancel\": \"İptal\",\n    \"global.change-password\": \"Şifreyi değiştir\",\n    \"global.content-manager\": \"İçerik Yöneticisi\",\n    \"global.continue\": \"Devam\",\n    \"global.delete\": \"Sil\",\n    \"global.delete-target\": \"Sil: {target}\",\n    \"global.description\": \"Tanım\",\n    \"global.details\": \"Detaylar\",\n    \"global.disabled\": \"Devredışı\",\n    \"global.documentation\": \"Dokümantasyon\",\n    \"global.enabled\": \"Etkin\",\n    \"global.finish\": \"Bitir\",\n    \"global.marketplace\": \"Pazaryeri\",\n    \"global.name\": \"İsim\",\n    \"global.none\": \"Hiçbiri\",\n    \"global.password\": \"Şifre\",\n    \"global.plugins\": \"Eklentiler\",\n    \"global.plugins.content-manager\": \"İçerik Yöneticisi\",\n    \"global.plugins.content-manager.description\": \"Veritabanındaki verileri görüntüleme, düzenleme ve silmenin kolay yolu.\",\n    \"global.plugins.content-type-builder\": \"İçerik Tipi Kurucusu\",\n    \"global.plugins.content-type-builder.description\": \"APInin veri yapısını modelle. Sadece bir iki dakikada yeni alanlar ve ilişkiler oluştur. Projendeki dosyalar otomatik olarak oluşturulur ve güncellenir.\",\n    \"global.plugins.documentation\": \"Dokümantasyon\",\n    \"global.plugins.documentation.description\": \"Bir OpenAPI Dokümanı oluştur ve SWAGGER UI ile APIni görselleştir.\",\n    \"global.plugins.email\": \"E-Posta\",\n    \"global.plugins.email.description\": \"Uygulamanı e-posta gönderecek şekilde ayarla.\",\n    \"global.plugins.graphql\": \"GraphQL\",\n    \"global.plugins.graphql.description\": \"Varsayılan API metodları ile bir GraphQL uç noktası ekler.\",\n    \"global.plugins.i18n\": \"Uluslararasılaştırma\",\n    \"global.plugins.i18n.description\": \"Bu eklenti, hem Yönetim paneli hem de API üzerinden, farklı dillerdeki içeriği oluşturma, okuma ve güncelleme imkanı sağlar.\",\n    \"global.plugins.sentry\": \"Sentry\",\n    \"global.plugins.sentry.description\": \"Strapi hata olaylarını Sentry'e ilet.\",\n    \"global.plugins.upload\": \"Ortam Kütüphanesi\",\n    \"global.plugins.upload.description\": \"Ortam dosyaları yönetimi.\",\n    \"global.plugins.users-permissions\": \"Roller ve İzinler\",\n    \"global.plugins.users-permissions.description\": \"Servisinizi JWT'ye dayalı tam bir kimlik doğrulama işlemi ile koruyun. Bu eklenti, kullanıcı grupları arasındaki izinleri yönetmenize izin veren bir ACL stratejisiyle de gelir.\",\n    \"global.profile\": \"Profil\",\n    \"global.prompt.unsaved\": \"Bu sayfadan ayrılmak istediğinize emin misiniz? Tüm düzenlemeleriniz kaybolacak\",\n    \"global.reset-password\": \"Şifreni sıfırla\",\n    \"global.roles\": \"Roller\",\n    \"global.save\": \"Kaydet\",\n    \"global.search\": \"Arama\",\n    \"global.see-more\": \"Daha fazla\",\n    \"global.select\": \"Seç\",\n    \"global.select-all-entries\": \"Tüm girdileri seç\",\n    \"global.settings\": \"Ayarlar\",\n    \"global.type\": \"Tip\",\n    \"global.users\": \"Kullanıcılar\",\n    light: light,\n    \"notification.contentType.relations.conflict\": \"İçerik tipinde çakışan ilişkiler var\",\n    \"notification.default.title\": \"Bilgi:\",\n    \"notification.error\": \"Bir hata oluştu\",\n    \"notification.error.layout\": \"Düzen alınamadı\",\n    \"notification.form.error.fields\": \"Form birden fazla hata içeriyor\",\n    \"notification.form.success.fields\": \"Değişiklikler kaydedildi\",\n    \"notification.link-copied\": \"Bağlantı panoya kopyalandı\",\n    \"notification.permission.not-allowed-read\": \"Bu dokümanı görme yetkin yok\",\n    \"notification.success.delete\": \"Öğe silindi\",\n    \"notification.success.saved\": \"Kaydedildi\",\n    \"notification.success.title\": \"Başarılı:\",\n    \"notification.success.apitokencreated\": \"API Token başarıyla oluşturuldu\",\n    \"notification.success.apitokenedited\": \"API Token başarıyla düzenlendi\",\n    \"notification.version.update.message\": \"Strapi'nin yeni versiyonu çıktı!\",\n    \"notification.warning.404\": \"404 - Bulunamadı\",\n    \"notification.warning.title\": \"Dikkat:\",\n    or: or,\n    \"request.error.model.unknown\": \"Bu model bulunmamaktadır.\",\n    skipToContent: skipToContent,\n    submit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, tr as default, light, or, skipToContent, submit };\n//# sourceMappingURL=tr.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,YAAY;AAChB,IAAI,gBAAgB;AACpB,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,qBAAqB;AACzB,IAAI,OAAO;AACX,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,kBAAkB;AACtB,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,gBAAgB;AACpB,IAAI,SAAS;AACb,IAAI,KAAK;AAAA,EACL;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,sBAAsB;AAAA,EACtB,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,qDAAqD;AAAA,EACrD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,+DAA+D;AAAA,EAC/D,mEAAmE;AAAA,EACnE,6DAA6D;AAAA,EAC7D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sDAAsD;AAAA,EACtD,mDAAmD;AAAA,EACnD,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,gEAAgE;AAAA,EAChE,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,iEAAiE;AAAA,EACjE,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,8DAA8D;AAAA,EAC9D,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,2DAA2D;AAAA,EAC3D,oCAAoC;AAAA,EACpC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,6CAA6C;AAAA,EAC7C,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,2CAA2C;AAAA,EAC3C,oDAAoD;AAAA,EACpD,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,sEAAsE;AAAA,EACtE,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC;AAAA,EACA,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB;AAAA,EACA,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACJ;", "names": []}