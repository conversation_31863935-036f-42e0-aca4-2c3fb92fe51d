{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/admin/translations/uk.json.mjs"], "sourcesContent": ["var configurations = \"налаштування\";\nvar from = \"з\";\nvar uk = {\n    \"attribute.boolean\": \"Boolean\",\n    \"attribute.boolean.description\": \"Так чи ні, 1 чи 0, правда чи брехня\",\n    \"attribute.component\": \"Компонент\",\n    \"attribute.component.description\": \"Група полей, які ви можете повторювати\",\n    \"attribute.customField\": \"Спеціальне поле\",\n    \"attribute.date\": \"Date\",\n    \"attribute.date.description\": \"Елемент вибору дати та часу\",\n    \"attribute.datetime\": \"Дата та час\",\n    \"attribute.dynamiczone\": \"Динамічна зона\",\n    \"attribute.dynamiczone.description\": \"Динамічний вибір компонентів підчас редагування контенту\",\n    \"attribute.email\": \"Email\",\n    \"attribute.email.description\": \"Електронна пошта з перевіркою формату\",\n    \"attribute.enumeration\": \"Enumeration\",\n    \"attribute.enumeration.description\": \"Перелік значень, вибирається одне\",\n    \"attribute.json\": \"JSON\",\n    \"attribute.json.description\": \"Інформація у форматі JSON\",\n    \"attribute.media\": \"Media\",\n    \"attribute.media.description\": \"Файли, як-то картинки, відео тощо\",\n    \"attribute.null\": \" \",\n    \"attribute.number\": \"Number\",\n    \"attribute.number.description\": \"Числа (integer, float, decimal)\",\n    \"attribute.password\": \"Password\",\n    \"attribute.password.description\": \"Поле паролю з шифруванням\",\n    \"attribute.relation\": \"Relation\",\n    \"attribute.relation.description\": \"Зв'язок з Типом Колекції\",\n    \"attribute.richtext\": \"Rich text\",\n    \"attribute.richtext.description\": \"Текст з можливістю форматування\",\n    \"attribute.blocks\": \"Багатий текст (блоки)\",\n    \"attribute.blocks.description\": \"Новий редактор тексту на основі JSON\",\n    \"attribute.text\": \"Text\",\n    \"attribute.text.description\": \"Короткий або довгий текст, як заголовок чи опис\",\n    \"attribute.time\": \"Time\",\n    \"attribute.timestamp\": \"Мітка часу\",\n    \"attribute.uid\": \"UID\",\n    \"attribute.uid.description\": \"Унікальний ідентифікатор\",\n    \"button.attributes.add.another\": \"Додате ще одне поле\",\n    \"button.component.add\": \"Додати компонент\",\n    \"button.component.create\": \"Створити новий компонент\",\n    \"button.model.create\": \"Створити Тип Колекції\",\n    \"button.single-types.create\": \"Створити Тип Одиниць\",\n    \"component.repeatable\": \"(повторюваний)\",\n    \"components.SelectComponents.displayed-value\": \"Вибрано {number, plural, =0 {# компонентів} one {# компонент} many {# конпонентів} other {# конпоненти}}\",\n    \"components.componentSelect.no-component-available\": \"Ви вже добавили всі компоненти\",\n    \"components.componentSelect.no-component-available.with-search\": \"Немає компонентів, які відповідають вашому запиту\",\n    \"components.componentSelect.value-component\": \"{number} вибраних компонентів (напишіть для пошуку)\",\n    \"components.componentSelect.value-components\": \"{number} вибраних компонентів\",\n    configurations: configurations,\n    \"contentType.apiId-plural.description\": \"Множинний API ID\",\n    \"contentType.apiId-plural.label\": \"API ID (Множина)\",\n    \"contentType.apiId-singular.description\": \"UID використовується для генерації маршрутів API та таблиць/колекцій баз даних\",\n    \"contentType.apiId-singular.label\": \"API ID (Однина)\",\n    \"contentType.collectionName.description\": \"Корисно, коли назва вашого Типу Вмісту та вашої таблиці різні\",\n    \"contentType.collectionName.label\": \"Назва колекції\",\n    \"contentType.displayName.label\": \"Назва для відображення\",\n    \"contentType.kind.change.warning\": \"Ви тільки що змінили Тип Вмісту: API буде перезавантажене (маршрути, контролери та сервіси будуть переписані).\",\n    \"error.attributeName.reserved-name\": \"Ця назва не може буди використана для вашого Типу Вмісту, так як вона може зламати іншу функціональність\",\n    \"error.contentType.pluralName-used\": \"Це значення не може співпадати зі словом у однині\",\n    \"error.contentType.singularName-used\": \"Це значення не може співпадати зі словом у множині\",\n    \"error.contentType.singularName-equals-pluralName\": \"Це значення не може співпадати з Множинним API ID іншого типу вмісту.\",\n    \"error.contentType.pluralName-equals-singularName\": \"Це значення не може співпадати з Singular API ID іншого типу вмісту.\",\n    \"error.contentType.pluralName-equals-collectionName\": \"Це значення вже використовується іншим типом вмісту.\",\n    \"error.contentTypeName.reserved-name\": \"Ця назва не може буди використана у вашому проекті, так як вона може зламати іншу функціональність\",\n    \"error.validation.enum-duplicate\": \"Значення не можуть повторюватись\",\n    \"error.validation.enum-empty-string\": \"Порожні рядки не допускаються\",\n    \"error.validation.enum-regex\": \"Принаймні одне значення недійсне. Значення повинні мати принаймні один алфавітний символ перед першою появою числа.\",\n    \"error.validation.minSupMax\": \"Не може бути більше\",\n    \"error.validation.positive\": \"Числом повинно бути додатнім\",\n    \"error.validation.regex\": \"Неправильний регулярний вираз\",\n    \"error.validation.relation.targetAttribute-taken\": \"Ця назва вже існує в цільовій моделі\",\n    \"form.attribute.component.option.add\": \"Додати компонент\",\n    \"form.attribute.component.option.create\": \"Додати новий компонент\",\n    \"form.attribute.component.option.create.description\": \"Компонент використовується в типах та інших компонентах, він буде доступний всюди.\",\n    \"form.attribute.component.option.repeatable\": \"Повторюваний компонент\",\n    \"form.attribute.component.option.repeatable.description\": \"Підходить для множинних об'єктів (масиву), наприклад, інгридієнтів, метатегів тощо...\",\n    \"form.attribute.component.option.reuse-existing\": \"Використати існуючий компонент\",\n    \"form.attribute.component.option.reuse-existing.description\": \"Використовуйте створений вами компонент, щоб підтримувати узгодженність данних серед різних Типів Вмісту.\",\n    \"form.attribute.component.option.single\": \"Одиничний компонент\",\n    \"form.attribute.component.option.single.description\": \"Підходить для групування полей, наприклад, повна адреса, основна інформація тощо...\",\n    \"form.attribute.item.customColumnName\": \"Власні назви стовпців\",\n    \"form.attribute.item.customColumnName.description\": \"Корисно для перейменування назв стовпців у базі даних для підтримки більш зрозумілого формату відповідей API\",\n    \"form.attribute.item.date.type.date\": \"date (нп: 01/01/{currentYear})\",\n    \"form.attribute.item.date.type.datetime\": \"datetime (нп: 01/01/{currentYear} 00:00)\",\n    \"form.attribute.item.date.type.time\": \"time (нп: 00:00)\",\n    \"form.attribute.item.defineRelation.fieldName\": \"Назва поля\",\n    \"form.attribute.item.enumeration.graphql\": \"Назва поля для GraphQL\",\n    \"form.attribute.item.enumeration.graphql.description\": \"Дозволяє перейменувати згенеровану для GraphQL назву поля\",\n    \"form.attribute.item.enumeration.placeholder\": \"Наприклад:\\nранок\\nдень\\nвечір\",\n    \"form.attribute.item.enumeration.rules\": \"Значення (одне на рядок)\",\n    \"form.attribute.item.maximum\": \"Максимальне значення\",\n    \"form.attribute.item.maximumLength\": \"Максимальна довжина\",\n    \"form.attribute.item.minimum\": \"Мінімальне значення\",\n    \"form.attribute.item.minimumLength\": \"Мінімальна довжина\",\n    \"form.attribute.item.number.type\": \"Формат числа\",\n    \"form.attribute.item.number.type.biginteger\": \"big integer (нп: 123456789)\",\n    \"form.attribute.item.number.type.decimal\": \"decimal (нп: 2.22)\",\n    \"form.attribute.item.number.type.float\": \"float (нп: 3.33333333)\",\n    \"form.attribute.item.number.type.integer\": \"integer (нп: 10)\",\n    \"form.attribute.item.privateField\": \"Приватне поле\",\n    \"form.attribute.item.privateField.description\": \"Це поле не буде відображатися у відповіді API\",\n    \"form.attribute.item.requiredField\": \"Обов'язкове поле\",\n    \"form.attribute.item.requiredField.description\": \"Ви не зможете створити запис якщо не заповните це поле\",\n    \"form.attribute.item.text.regex\": \"Регулярний вираз (RegExp)\",\n    \"form.attribute.item.text.regex.description\": \"Шаблон регулярного виразу.\",\n    \"form.attribute.item.uniqueField\": \"Унікальне поле\",\n    \"form.attribute.item.uniqueField.description\": \"Ви не зможете створити запис, якщо вже існує запис із таким самим значенням поля\",\n    \"form.attribute.item.uniqueField.v5.willBeDisabled'\": \"Унікальні поля наразі не працюють належним чином у компонентах. Якщо ви вимкнете цю функцію, поле буде вимкнено до виправлення.\",\n    \"form.attribute.item.uniqueField.v5.disabled\": \"Унікальні поля наразі не працюють належним чином у компонентах. Це поле було вимкнено до виправлення.\",\n    \"form.attribute.media.allowed-types\": \"Виберіть дозволені типи медіа\",\n    \"form.attribute.media.allowed-types.option-files\": \"Файли\",\n    \"form.attribute.media.allowed-types.option-images\": \"Картинки\",\n    \"form.attribute.media.allowed-types.option-videos\": \"Відео\",\n    \"form.attribute.media.option.multiple\": \"Множинні медіа\",\n    \"form.attribute.media.option.multiple.description\": \"Підходить для слайдерів, каруселей або завантаження кількох файлів\",\n    \"form.attribute.media.option.single\": \"Одиничне медіа\",\n    \"form.attribute.media.option.single.description\": \"Підходить для аватарок, картинок профіля або обкладинок\",\n    \"form.attribute.settings.default\": \"Значення за замовчуванням\",\n    \"form.attribute.text.option.long-text\": \"Довгий текст\",\n    \"form.attribute.text.option.long-text.description\": \"Підходить для описів, тексту про себе. Точний пошук вимкнено.\",\n    \"form.attribute.text.option.short-text\": \"Короткий текст\",\n    \"form.attribute.text.option.short-text.description\": \"Підходить для назв, імен, посиалань (URL). Дозволяє точний пошук по цьому полю.\",\n    \"form.button.add-components-to-dynamiczone\": \"Додати компоненти у зону.\",\n    \"form.button.add-field\": \"Додати ще одне поле\",\n    \"form.button.add-first-field-to-created-component\": \"Додати перше поле компоненту\",\n    \"form.button.add.field.to.collectionType\": \"Додати ще одне поле до цього Типу Колекції\",\n    \"form.button.add.field.to.component\": \"Додати ще одне поле до цього компоненту\",\n    \"form.button.add.field.to.contentType\": \"Додати ще одне поле до цього Типу Вмісту\",\n    \"form.button.add.field.to.singleType\": \"Додати ще одне поле до цього Типу Одиниць\",\n    \"form.button.cancel\": \"Скасувати\",\n    \"form.button.collection-type.description\": \"Підходить для множинних об'єктів, як-то дописи, товари, коментарі тощо.\",\n    \"form.button.collection-type.name\": \"Collection Type\",\n    \"form.button.configure-component\": \"Налаштувати компонент\",\n    \"form.button.configure-view\": \"Налаштувати вигляд\",\n    \"form.button.select-component\": \"Вибрати компонент\",\n    \"form.button.single-type.description\": \"Підходить для поодиноких об'єктів, як-то домашня сторінка, про нас тощо\",\n    \"form.button.single-type.name\": \"Тип Одиниці\",\n    from: from,\n    \"listView.headerLayout.description\": \"Створіть архітектуру даних вашого вмісту\",\n    \"menu.section.components.name\": \"Компоненти\",\n    \"menu.section.models.name\": \"Типи колекцій\",\n    \"menu.section.single-types.name\": \"Типи одиниць\",\n    \"modalForm.attribute.form.base.name.description\": \"Для назви атрибута не допускається пробілів\",\n    \"modalForm.attribute.form.base.name.placeholder\": \"наприклад, slug, seoUrl, canonicalUrl\",\n    \"modalForm.attribute.target-field\": \"Пов'язане поле\",\n    \"modalForm.attributes.select-component\": \"Виберіть компонент\",\n    \"modalForm.attributes.select-components\": \"Виберіть компоненти\",\n    \"modalForm.collectionType.header-create\": \"Створіть тип колекції\",\n    \"modalForm.component.header-create\": \"Створити компонент\",\n    \"modalForm.components.create-component.category.label\": \"Виберіть категорію або введіть назву для створення нової\",\n    \"modalForm.components.icon.label\": \"Іконка\",\n    \"modalForm.empty.button\": \"Додати користувацькі поля\",\n    \"modalForm.empty.heading\": \"Тут ще нічого немає.\",\n    \"modalForm.empty.sub-heading\": \"Знайдіть те, що шукаєте, за допомогою широкого спектру розширень.\",\n    \"modalForm.editCategory.base.name.description\": \"Для назви категорії не допускається пробілів\",\n    \"modalForm.header-edit\": \"Редагувати {name}\",\n    \"modalForm.header.categories\": \"Категорії\",\n    \"modalForm.header.back\": \"Назад\",\n    \"modalForm.singleType.header-create\": \"Створити Тип Одиниць\",\n    \"modalForm.sub-header.addComponentToDynamicZone\": \"Додати новий компонент до динамічної зони\",\n    \"modalForm.sub-header.attribute.create\": \"Додати нове поле {type}\",\n    \"modalForm.sub-header.attribute.create.step\": \"Додати новий компонент ({step}/2)\",\n    \"modalForm.sub-header.attribute.edit\": \"Редагувати {name}\",\n    \"modalForm.sub-header.chooseAttribute.collectionType\": \"Виберіть поле для вашого Типу Колекції\",\n    \"modalForm.sub-header.chooseAttribute.component\": \"Виберіть поле для вашого компоненту\",\n    \"modalForm.sub-header.chooseAttribute.singleType\": \"Виберіть поле для вашого Тип Одиниць\",\n    \"modalForm.custom-fields.advanced.settings.extended\": \"Extended settings\",\n    \"modalForm.tabs.custom\": \"Custom\",\n    \"modalForm.tabs.custom.howToLink\": \"How to add custom fields\",\n    \"modalForm.tabs.default\": \"Default\",\n    \"modalForm.tabs.label\": \"Default and Custom types tabs\",\n    \"modelPage.attribute.relation-polymorphic\": \"Зв'язок (поліморфний)\",\n    \"modelPage.attribute.relationWith\": \"Зв'язок з\",\n    \"notification.error.dynamiczone-min.validation\": \"Принаймні один компонент потрібен у динамічній зоні, щоб мати можливість зберегти тип вмісту\",\n    \"notification.info.autoreaload-disable\": \"Функція autoReload має буте включена. Будь ласка, запустіть свій додаток вікористовуючи `strapi develop`.\",\n    \"notification.info.creating.notSaved\": \"Будь ласка, збережіть ваші зміни перед тим як створювати новий компонент або Тип Колекції\",\n    \"plugin.description.long\": \"Моделюйте структуру данних для вашого API. Створюйте нові поля та зв'язки за хвилину. Файли будуть автоматично створені та оновлені в вашему проекту.\",\n    \"plugin.description.short\": \"Моделюйте структуру данних для вашого API.\",\n    \"plugin.name\": \"Будівельник типу вмісту\",\n    \"popUpForm.navContainer.advanced\": \"Розширені налаштування\",\n    \"popUpForm.navContainer.base\": \"Основне\",\n    \"popUpWarning.bodyMessage.cancel-modifications\": \"Ви впевнені, що хочете скасувати свої зміни?\",\n    \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Ви впевнені, що хочете скасувати свої зміни? Деякі компоненти були змінені, або створені нові...\",\n    \"popUpWarning.bodyMessage.category.delete\": \"Ви впевнені, що хочете видалити цю категорію? Всі компоненти також будуть видалені.\",\n    \"popUpWarning.bodyMessage.component.delete\": \"Ви впевнені, що хочете видалити цей компонент?\",\n    \"popUpWarning.bodyMessage.contentType.delete\": \"Ви впевнені, що хочете видалити цей типу Колекції?\",\n    \"popUpWarning.draft-publish.button.confirm\": \"Так, вимкнути\",\n    \"popUpWarning.draft-publish.message\": \"Якщо ви вимкнете 'Чернетка та публікація', ваші чернетки будуть видалені.\",\n    \"popUpWarning.draft-publish.second-message\": \"Ви впевнені, що хочете вимкнути це?\",\n    \"prompt.unsaved\": \"Ви впевнені що хочете залишити сторінку? Всі виші зміни будуть втарчені.\",\n    \"relation.attributeName.placeholder\": \"Нп: автор, категорія, тег\",\n    \"relation.manyToMany\": \"містить і належить багатьом\",\n    \"relation.manyToOne\": \"містить багато\",\n    \"relation.manyWay\": \"містить багато\",\n    \"relation.oneToMany\": \"належить до багатьох\",\n    \"relation.oneToOne\": \"містить і належить до однієї\",\n    \"relation.oneWay\": \"містить одне\",\n    \"table.button.no-fields\": \"Add new field\",\n    \"table.content.create-first-content-type\": \"Створіть свій перший Тип колекції\",\n    \"table.content.no-fields.collection-type\": \"Додайте своє перше поле до цього Типу колекції\",\n    \"table.content.no-fields.component\": \"Додайте своє перше поле до цього компонента\",\n    \"IconPicker.search.placeholder.label\": \"Пошук іконки\",\n    \"IconPicker.search.clear.label\": \"Очистити пошук іконок\",\n    \"IconPicker.search.button.label\": \"Кнопка пошуку іконки\",\n    \"IconPicker.remove.tooltip\": \"Вилучити обрану іконку\",\n    \"IconPicker.remove.button\": \"Вилучити кнопку обраної іконки\",\n    \"IconPicker.emptyState.label\": \"Іконка не знайдена\",\n    \"IconPicker.icon.label\": \"Виберіть іконку {icon}\"\n};\n\nexport { configurations, uk as default, from };\n//# sourceMappingURL=uk.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,iBAAiB;AACrB,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,wCAAwC;AAAA,EACxC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,sDAAsD;AAAA,EACtD,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC;AAAA,EACA,qCAAqC;AAAA,EACrC,gCAAgC;AAAA,EAChC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,yBAAyB;AAAA,EACzB,mCAAmC;AAAA,EACnC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,yBAAyB;AAC7B;", "names": []}