'use client';

import React, { useState } from 'react';
import { Star, MapPin, Clock, Phone, Globe, ExternalLink, ChevronDown, ChevronUp } from 'lucide-react';

interface NearbyPlace {
  place_id: string;
  name: string;
  vicinity: string;
  rating?: number;
  user_ratings_total?: number;
  price_level?: number;
  types: string[];
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  photos?: Array<{
    photo_reference: string;
    width: number;
    height: number;
  }>;
  opening_hours?: {
    open_now: boolean;
  };
  business_status?: string;
}

interface PlaceCategory {
  id: number;
  name: string;
  displayName: string;
  icon: string;
  color: string;
}

interface NearbyPlacesSection {
  category: PlaceCategory;
  places: NearbyPlace[];
  error?: string;
}

interface NearbyPlacesProps {
  nearbyPlaces: Record<string, NearbyPlacesSection>;
  className?: string;
  showHeader?: boolean;
}

const PlaceCard: React.FC<{ place: NearbyPlace }> = ({ place }) => {
  const getPhotoUrl = (photoReference: string) => {
    const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY;
    if (!apiKey || !photoReference) return null;
    return `https://maps.googleapis.com/maps/api/place/photo?maxwidth=300&photo_reference=${photoReference}&key=${apiKey}`;
  };

  const getPriceLevelText = (level?: number) => {
    if (!level) return null;
    return '$'.repeat(level);
  };

  const openInGoogleMaps = () => {
    const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(place.name)}&query_place_id=${place.place_id}`;
    window.open(url, '_blank');
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg hover:shadow-md transition-all duration-200 hover:border-gray-300">
      <div className="flex">
        {/* Photo */}
        <div className="w-20 h-20 flex-shrink-0">
          {place.photos && place.photos.length > 0 ? (
            <img
              src={getPhotoUrl(place.photos[0].photo_reference) || '/placeholder-business.jpg'}
              alt={place.name}
              className="w-full h-full object-cover rounded-l-lg"
              onError={(e) => {
                (e.target as HTMLImageElement).src = '/placeholder-business.jpg';
              }}
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 rounded-l-lg flex items-center justify-center">
              <MapPin className="h-6 w-6 text-gray-400" />
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 p-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="font-medium text-gray-900 text-sm leading-tight mb-1 line-clamp-1">
                {place.name}
              </h3>
              
              {/* Rating */}
              {place.rating && (
                <div className="flex items-center space-x-1 mb-1">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-3 w-3 ${
                          i < Math.floor(place.rating!)
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-xs text-gray-600">
                    {place.rating.toFixed(1)}
                  </span>
                  {place.user_ratings_total && (
                    <span className="text-xs text-gray-500">
                      ({place.user_ratings_total})
                    </span>
                  )}
                </div>
              )}

              {/* Address */}
              <p className="text-xs text-gray-500 mb-2 line-clamp-1">{place.vicinity}</p>

              {/* Status and Price */}
              <div className="flex items-center space-x-2">
                {place.opening_hours && (
                  <span className={`text-xs px-2 py-0.5 rounded-full font-medium ${
                    place.opening_hours.open_now
                      ? 'bg-green-100 text-green-700'
                      : 'bg-red-100 text-red-700'
                  }`}>
                    {place.opening_hours.open_now ? 'Open' : 'Closed'}
                  </span>
                )}

                {place.price_level && (
                  <span className="text-xs text-gray-500 font-medium">
                    {getPriceLevelText(place.price_level)}
                  </span>
                )}
              </div>
            </div>

            {/* Actions */}
            <button
              onClick={openInGoogleMaps}
              className="ml-2 p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-all duration-200"
              title="View on Google Maps"
            >
              <ExternalLink className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

const CategorySection: React.FC<{ section: NearbyPlacesSection }> = ({ section }) => {
  const [isExpanded, setIsExpanded] = useState(true);

  if (section.error) {
    return (
      <div className="mb-6">
        <div className="flex items-center space-x-2 mb-3">
          <span className="text-lg">{section.category.icon}</span>
          <h2 className="text-lg font-semibold text-gray-900">
            {section.category.displayName}
          </h2>
        </div>
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">Error loading places: {section.error}</p>
        </div>
      </div>
    );
  }

  if (section.places.length === 0) {
    return (
      <div className="mb-6">
        <div className="flex items-center space-x-2 mb-3">
          <span className="text-lg">{section.category.icon}</span>
          <h2 className="text-lg font-semibold text-gray-900">
            {section.category.displayName}
          </h2>
        </div>
        <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <p className="text-sm text-gray-600">No places found in this category</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-4">
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center justify-between w-full mb-3 p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors duration-200 group"
      >
        <div className="flex items-center space-x-3">
          <span className="text-xl">{section.category.icon}</span>
          <div className="text-left">
            <h2 className="text-base font-semibold text-gray-900">
              {section.category.displayName}
            </h2>
            <p className="text-xs text-gray-500">{section.places.length} places found</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span
            className="px-2 py-1 text-xs font-medium rounded-full text-white"
            style={{ backgroundColor: section.category.color }}
          >
            {section.places.length}
          </span>
          {isExpanded ? (
            <ChevronUp className="h-4 w-4 text-gray-500 group-hover:text-gray-700" />
          ) : (
            <ChevronDown className="h-4 w-4 text-gray-500 group-hover:text-gray-700" />
          )}
        </div>
      </button>

      {isExpanded && (
        <div className="space-y-2 pl-2">
          {section.places.map((place) => (
            <PlaceCard key={place.place_id} place={place} />
          ))}
        </div>
      )}
    </div>
  );
};

export const NearbyPlaces: React.FC<NearbyPlacesProps> = ({ nearbyPlaces, className = '', showHeader = true }) => {
  const sections = Object.values(nearbyPlaces);

  if (sections.length === 0) {
    return (
      <div className={`p-6 bg-gray-50 border border-gray-200 rounded-lg ${className}`}>
        <div className="text-center">
          <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-3" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Nearby Places</h3>
          <p className="text-gray-600">
            Set property coordinates and generate nearby places to see what's around this location.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      {showHeader && (
        <div className="mb-6">
          <h2 className="text-xl font-bold text-gray-900 mb-2">What's Nearby</h2>
          <p className="text-gray-600">
            Discover restaurants, schools, shopping, and more around this property.
          </p>
        </div>
      )}

      <div className="space-y-4">
        {sections.map((section) => (
          <CategorySection key={section.category.name} section={section} />
        ))}
      </div>
    </div>
  );
};
