"use strict";
/**
 * membership service
 */
Object.defineProperty(exports, "__esModule", { value: true });
const strapi_1 = require("@strapi/strapi");
exports.default = strapi_1.factories.createCoreService('api::membership.membership', ({ strapi }) => ({
    // Get user's membership limits
    async getUserLimits(userId) {
        try {
            const user = await strapi.entityService.findOne('plugin::users-permissions.user', userId, {
                populate: {
                    membership: true
                }
            });
            if (!(user === null || user === void 0 ? void 0 : user.membership)) {
                // Default limits for users without membership
                return {
                    maxProperties: 3,
                    maxImages: 3,
                    canCreateProjects: false,
                    canAccessAnalytics: false,
                    canUsePremiumFilters: false,
                };
            }
            return {
                maxProperties: user.membership.maxProperties,
                maxImages: user.membership.maxImages,
                canCreateProjects: user.membership.canCreateProjects,
                canAccessAnalytics: user.membership.canAccessAnalytics,
                canUsePremiumFilters: user.membership.canUsePremiumFilters,
            };
        }
        catch (error) {
            console.error('Error getting user limits:', error);
            // Return default limits on error
            return {
                maxProperties: 3,
                maxImages: 3,
                canCreateProjects: false,
                canAccessAnalytics: false,
                canUsePremiumFilters: false,
            };
        }
    },
    // Check if user can create property
    async canCreateProperty(userId) {
        try {
            const limits = await this.getUserLimits(userId);
            const userProperties = await strapi.entityService.findMany('api::property.property', {
                filters: {
                    owner: {
                        id: userId
                    }
                }
            });
            return userProperties.length < limits.maxProperties;
        }
        catch (error) {
            console.error('Error checking property creation permission:', error);
            return false;
        }
    },
    // Check if user can upload more images
    async canUploadImages(userId, currentImageCount) {
        try {
            const limits = await this.getUserLimits(userId);
            return currentImageCount < limits.maxImages;
        }
        catch (error) {
            console.error('Error checking image upload permission:', error);
            return false;
        }
    },
    // Check if membership is expired
    async isMembershipExpired(userId) {
        try {
            const user = await strapi.entityService.findOne('plugin::users-permissions.user', userId, {
                populate: {
                    membership: true
                }
            });
            if (!(user === null || user === void 0 ? void 0 : user.membership) || !(user === null || user === void 0 ? void 0 : user.membershipEndDate)) {
                return true;
            }
            return new Date() > new Date(user.membershipEndDate);
        }
        catch (error) {
            console.error('Error checking membership expiration:', error);
            return true;
        }
    }
}));
