'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import DashboardLayout from '@/components/Dashboard/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import { propertiesAPI } from '@/lib/api';
import { SimpleNeighborhoodInput } from '@/components/SimpleNeighborhoodInput';
import { CoordinateSelector } from '@/components/CoordinateSelector';
import { MapPreviewWithNearbyPlaces } from '@/components/MapPreviewWithNearbyPlaces';
import ImageUploadWithDragDrop from '@/components/ImageUploadWithDragDrop';
import FloorPlanUploadWithDragDrop from '@/components/FloorPlanUploadWithDragDrop';
import { ArrowLeft, Save, Eye } from 'lucide-react';
import Link from 'next/link';

interface PropertyFormData {
  title: string;
  description: string;
  price: number;
  currency: string;
  propertyType: string;
  status: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  areaUnit: string;
  address: string;
  city: string;
  country: string;
  neighborhood: string[];
  coordinates?: { lat: number; lng: number };
  propertyCode: string;
  isLuxury: boolean;
  features: string[];
  yearBuilt?: number;
  parking?: number;
  furnished: boolean;
  petFriendly: boolean;
  virtualTour?: string;
  images?: FileList | null;
  floorPlan?: File | null;
  existingImages?: any[];
  existingFloorPlan?: any;
}

const EditPropertyPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const { user, isAuthenticated, authLoading } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const [formData, setFormData] = useState<PropertyFormData>({
    title: '',
    description: '',
    price: 0,
    currency: 'USD',
    propertyType: 'apartment',
    status: 'for-sale',
    bedrooms: 1,
    bathrooms: 1,
    area: 0,
    areaUnit: 'sqm',
    address: '',
    city: '',
    country: '',
    neighborhood: [],
    coordinates: undefined,
    propertyCode: '',
    isLuxury: false,
    features: [],
    yearBuilt: undefined,
    parking: undefined,
    furnished: false,
    petFriendly: false,
    virtualTour: '',
    images: null,
    floorPlan: null,
    existingImages: [],
    existingFloorPlan: null
  });

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/login?redirect=/dashboard/properties');
    }
  }, [isAuthenticated, authLoading, router]);

  useEffect(() => {
    const fetchProperty = async () => {
      if (!params.id) return;

      try {
        const response = await propertiesAPI.getForEdit(params.id as string);
        console.log('Full API response:', response);
        // The backend returns { data: property }, so we need response.data
        const property = response.data;
        console.log('Loaded property data:', property);

        if (!property) {
          throw new Error('Property data not found');
        }

        // Normalize neighborhood data
        let neighborhoods: string[] = [];
        if (property.neighborhood) {
          if (typeof property.neighborhood === 'string') {
            neighborhoods = [property.neighborhood];
          } else if (Array.isArray(property.neighborhood)) {
            if (property.neighborhood.length > 0 && typeof property.neighborhood[0] === 'string') {
              neighborhoods = property.neighborhood;
            } else {
              // Convert object format to string array
              neighborhoods = property.neighborhood.map((n: any) => n.name || n);
            }
          }
        }

        const formDataToSet = {
          title: property.title || '',
          description: property.description || '',
          price: Number(property.price) || 0,
          currency: property.currency || 'USD',
          propertyType: property.propertyType || 'apartment',
          status: property.status || 'for-sale',
          bedrooms: Number(property.bedrooms) || 1,
          bathrooms: Number(property.bathrooms) || 1,
          area: Number(property.area) || 0,
          areaUnit: property.areaUnit || 'sqm',
          address: property.address || '',
          city: property.city || '',
          country: property.country || '',
          neighborhood: neighborhoods,
          coordinates: property.coordinates,
          propertyCode: property.propertyCode || '',
          isLuxury: Boolean(property.isLuxury),
          features: Array.isArray(property.features) ? property.features : [],
          yearBuilt: property.yearBuilt ? Number(property.yearBuilt) : undefined,
          parking: property.parking ? Number(property.parking) : undefined,
          furnished: Boolean(property.furnished),
          petFriendly: Boolean(property.petFriendly),
          virtualTour: property.virtualTour || '',
          images: null, // Keep as null for new uploads
          floorPlan: null, // Keep as null for new uploads
          existingImages: property.images || [], // Store existing images separately
          existingFloorPlan: property.floorPlan || null // Store existing floor plan separately
        };

        console.log('Setting form data:', formDataToSet);
        setFormData(formDataToSet);
      } catch (err: any) {
        setError(err.response?.data?.error?.message || 'Failed to load property');
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchProperty();
    }
  }, [params.id, user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseFloat(value) || 0 }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleFeatureToggle = (feature: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.includes(feature)
        ? prev.features.filter(f => f !== feature)
        : [...prev.features, feature]
    }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    setFormData(prev => ({ ...prev, images: files }));
  };

  const handleFloorPlanChange = (file: File | null) => {
    setFormData(prev => ({ ...prev, floorPlan: file }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      // Validate required fields
      if (!formData.title.trim()) {
        throw new Error('Property title is required');
      }
      if (!formData.description.trim()) {
        throw new Error('Property description is required');
      }
      if (formData.price <= 0) {
        throw new Error('Property price must be greater than 0');
      }
      if (formData.area <= 0) {
        throw new Error('Property area must be greater than 0');
      }
      if (!formData.address.trim()) {
        throw new Error('Property address is required');
      }
      if (!formData.city.trim()) {
        throw new Error('City is required');
      }
      if (!formData.country.trim()) {
        throw new Error('Country is required');
      }

      // Prepare property data with proper type conversion
      const propertyData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        price: Number(formData.price),
        currency: formData.currency,
        propertyType: formData.propertyType,
        status: formData.status,
        bedrooms: Number(formData.bedrooms),
        bathrooms: Number(formData.bathrooms),
        area: Number(formData.area),
        areaUnit: formData.areaUnit,
        address: formData.address.trim(),
        city: formData.city.trim(),
        country: formData.country.trim(),
        neighborhood: formData.neighborhood,
        coordinates: formData.coordinates,
        propertyCode: formData.propertyCode.trim(),
        isLuxury: formData.isLuxury,
        features: formData.features,
        yearBuilt: formData.yearBuilt ? Number(formData.yearBuilt) : null,
        parking: formData.parking ? Number(formData.parking) : null,
        furnished: formData.furnished,
        petFriendly: formData.petFriendly,
        virtualTour: formData.virtualTour?.trim() || null
      };

      // Use FormData for file uploads
      if (formData.images || formData.floorPlan) {
        const formDataToSend = new FormData();
        formDataToSend.append('data', JSON.stringify(propertyData));

        // Add images if selected
        if (formData.images && formData.images.length > 0) {
          for (let i = 0; i < formData.images.length; i++) {
            formDataToSend.append('files.images', formData.images[i]);
          }
        }

        // Add floor plan if selected
        if (formData.floorPlan) {
          formDataToSend.append('files.floorPlan', formData.floorPlan);
        }

        // Submit with files using fetch (required for file uploads)
        const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/api/properties/${params.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('jwt')}`
          },
          body: formDataToSend
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error?.message || 'Failed to update property');
        }

        const result = await response.json();
        console.log('Property updated with files:', result);
      } else {
        // Submit without files using API wrapper
        const result = await propertiesAPI.update(params.id as string, propertyData);
        console.log('Property updated:', result);
      }

      setSuccess(true);
      setTimeout(() => {
        router.push('/dashboard/properties');
      }, 2000);

    } catch (err: any) {
      console.error('Error updating property:', err);
      setError(err.message || 'Failed to update property');
    } finally {
      setSaving(false);
    }
  };

  const propertyTypes = [
    'apartment', 'villa', 'townhouse', 'penthouse', 'studio', 'duplex', 'land', 'commercial'
  ];

  const statusOptions = [
    'for-sale', 'for-rent', 'sold', 'rented', 'off-market'
  ];

  const availableFeatures = [
    'Swimming Pool', 'Gym', 'Garden', 'Balcony', 'Terrace', 'Garage', 
    'Security', 'Elevator', 'Air Conditioning', 'Heating', 'Fireplace',
    'Walk-in Closet', 'Storage Room', 'Laundry Room', 'Maid Room'
  ];

  if (authLoading || loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (error && !formData.title) {
    return (
      <DashboardLayout>
        <div className="max-w-2xl mx-auto">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600">{error}</p>
            <Link
              href="/dashboard/properties"
              className="inline-flex items-center mt-3 text-blue-600 hover:text-blue-800"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Properties
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link
              href="/dashboard/properties"
              className="inline-flex items-center text-gray-600 hover:text-gray-900"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Properties
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Edit Property</h1>
              {formData.title && (
                <h3 className="text-lg font-medium text-gray-700 mt-1">
                  {formData.title}
                  {formData.propertyCode && (
                    <span className="text-sm text-gray-500 ml-2">
                      (ID: {formData.propertyCode})
                    </span>
                  )}
                </h3>
              )}
              <p className="text-gray-600 mt-1">Update your property listing details</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <Link
              href={`/properties/${params.id}`}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Link>
          </div>
        </div>

        {/* Success Message */}
        {success && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <p className="text-green-600">Property updated successfully! Redirecting...</p>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 space-y-8">
          {/* Basic Information */}
          <div className="space-y-6">
            <h2 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
              Basic Information
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Property Title *
                </label>
                <input
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., Luxury 3BR Apartment in Downtown"
                />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe your property..."
                />
              </div>
            </div>
          </div>

          {/* Price and Property Details */}
          <div className="space-y-6">
            <h2 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
              Price & Details
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price *
                </label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  required
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Currency
                </label>
                <select
                  name="currency"
                  value={formData.currency}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="USD">USD</option>
                  <option value="EUR">EUR</option>
                  <option value="GBP">GBP</option>
                  <option value="AED">AED</option>
                  <option value="SAR">SAR</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Property Type *
                </label>
                <select
                  name="propertyType"
                  value={formData.propertyType}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {propertyTypes.map(type => (
                    <option key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {statusOptions.map(status => (
                    <option key={status} value={status}>
                      {status.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Bedrooms
                </label>
                <input
                  type="number"
                  name="bedrooms"
                  value={formData.bedrooms}
                  onChange={handleInputChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Bathrooms
                </label>
                <input
                  type="number"
                  name="bathrooms"
                  value={formData.bathrooms}
                  onChange={handleInputChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Area *
                </label>
                <input
                  type="number"
                  name="area"
                  value={formData.area}
                  onChange={handleInputChange}
                  required
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Area Unit
                </label>
                <select
                  name="areaUnit"
                  value={formData.areaUnit}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="sqm">Square Meters</option>
                  <option value="sqft">Square Feet</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Year Built
                </label>
                <input
                  type="number"
                  name="yearBuilt"
                  value={formData.yearBuilt || ''}
                  onChange={handleInputChange}
                  min="1800"
                  max={new Date().getFullYear()}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Parking Spaces
                </label>
                <input
                  type="number"
                  name="parking"
                  value={formData.parking || ''}
                  onChange={handleInputChange}
                  min="0"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Virtual Tour URL
                </label>
                <input
                  type="url"
                  name="virtualTour"
                  value={formData.virtualTour}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="https://..."
                />
              </div>
            </div>
          </div>

          {/* Location */}
          <div className="space-y-6">
            <h2 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
              Location
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Address *
                </label>
                <input
                  type="text"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  City *
                </label>
                <input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Country *
                </label>
                <input
                  type="text"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="md:col-span-3">
                <SimpleNeighborhoodInput
                  value={formData.neighborhood}
                  onChange={(neighborhoods) => setFormData(prev => ({ ...prev, neighborhood: neighborhoods }))}
                  maxSelections={3}
                  placeholder="Enter neighborhood name..."
                />
              </div>

              <div className="md:col-span-3">
                <CoordinateSelector
                  value={formData.coordinates}
                  onChange={(coordinates) => setFormData(prev => ({ ...prev, coordinates }))}
                  address={`${formData.address}, ${formData.city}, ${formData.country}`.trim().replace(/^,|,$/, '')}
                />
              </div>

              <div className="md:col-span-3">
                <MapPreviewWithNearbyPlaces
                  coordinates={formData.coordinates}
                  address={`${formData.address}, ${formData.city}, ${formData.country}`.trim().replace(/^,|,$/, '')}
                  className="mt-4"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Property Code
                </label>
                <input
                  type="text"
                  name="propertyCode"
                  value={formData.propertyCode}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="e.g., PROP-001"
                />
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="space-y-6">
            <h2 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
              Features & Amenities
            </h2>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {availableFeatures.map(feature => (
                <label key={feature} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.features.includes(feature)}
                    onChange={() => handleFeatureToggle(feature)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700">{feature}</span>
                </label>
              ))}
            </div>

            {/* Additional Info */}
            <div className="pt-4">
              <h3 className="text-base font-semibold text-gray-900 mb-4">Additional Info</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    name="isLuxury"
                    checked={formData.isLuxury}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm font-medium text-gray-700">Luxury Property</span>
                </label>

                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    name="furnished"
                    checked={formData.furnished}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm font-medium text-gray-700">Furnished</span>
                </label>

                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    name="petFriendly"
                    checked={formData.petFriendly}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm font-medium text-gray-700">Pet Friendly</span>
                </label>
              </div>
            </div>
          </div>

          {/* Media Upload */}
          <div className="space-y-6">
            <h2 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
              Media Upload
            </h2>

            {/* Property Images - Full Width */}
            <ImageUploadWithDragDrop
              onImagesChange={(files) => setFormData(prev => ({ ...prev, images: files }))}
              maxImages={15}
              className="w-full mb-6"
            />

            {/* Floor Plan Upload - Full Width with Drag & Drop */}
            <FloorPlanUploadWithDragDrop
              onFloorPlanChange={handleFloorPlanChange}
              currentFloorPlan={formData.floorPlan}
              className="w-full"
            />
          </div>

          {/* Submit Button */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
            <Link
              href="/dashboard/properties"
              className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={saving}
              className="inline-flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
};

export default EditPropertyPage;
