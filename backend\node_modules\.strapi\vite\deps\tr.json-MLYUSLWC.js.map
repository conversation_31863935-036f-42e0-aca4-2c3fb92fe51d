{"version": 3, "sources": ["../../../@strapi/email/dist/admin/translations/tr.json.mjs"], "sourcesContent": ["var link = \"Bağlantı\";\nvar tr = {\n    link: link,\n    \"Settings.email.plugin.button.test-email\": \"Deneme e-postası gönder\",\n    \"Settings.email.plugin.label.defaultFrom\": \"Varsayılan gönderim adresi\",\n    \"Settings.email.plugin.label.defaultReplyTo\": \"Varsayılan yanıt adresi\",\n    \"Settings.email.plugin.label.provider\": \"E-Posta sağlayıcı\",\n    \"Settings.email.plugin.label.testAddress\": \"Alıcı e-posta adresi\",\n    \"Settings.email.plugin.notification.config.error\": \"E-posta ayarlarını okuma hatası\",\n    \"Settings.email.plugin.notification.data.loaded\": \"E-posta ayarları yüklendi\",\n    \"Settings.email.plugin.notification.test.error\": \"{to} adresine deneme e-postası gönderimi başarısız oldu\",\n    \"Settings.email.plugin.notification.test.success\": \"E-posta denemesi başarılı. {to} adresinin posta kutusunu kontrol edin\",\n    \"Settings.email.plugin.placeholder.defaultFrom\": \"ör: Strap<PERSON> <<EMAIL>>\",\n    \"Settings.email.plugin.placeholder.defaultReplyTo\": \"ör: Strapi <<EMAIL>>\",\n    \"Settings.email.plugin.placeholder.testAddress\": \"ör: <EMAIL>\",\n    \"Settings.email.plugin.subTitle\": \"E-posta eklentisinin ayarlarını deneyin\",\n    \"Settings.email.plugin.text.configuration\": \"Plugin {file} dosyası üzerinden ayarlanıyor. Detaylar için şu bağlantıya bakın: {link}\",\n    \"Settings.email.plugin.title\": \"Kurulum\",\n    \"Settings.email.plugin.title.config\": \"Kurulum\",\n    \"Settings.email.plugin.title.test\": \"E-posta gönderimini dene\",\n    \"SettingsNav.link.settings\": \"Ayarlar\",\n    \"SettingsNav.section-label\": \"E-posta eklentisi\",\n    \"components.Input.error.validation.email\": \"Geçersiz e-posta adresi\"\n};\n\nexport { tr as default, link };\n//# sourceMappingURL=tr.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,OAAO;AACX,IAAI,KAAK;AAAA,EACL;AAAA,EACA,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,iDAAiD;AAAA,EACjD,oDAAoD;AAAA,EACpD,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,2CAA2C;AAC/C;", "names": []}