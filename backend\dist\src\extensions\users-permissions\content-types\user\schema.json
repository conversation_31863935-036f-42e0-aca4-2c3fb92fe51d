{"kind": "collectionType", "collectionName": "up_users", "info": {"name": "user", "description": "", "singularName": "user", "pluralName": "users", "displayName": "User"}, "options": {"draftAndPublish": false}, "attributes": {"username": {"type": "string", "minLength": 3, "unique": true, "configurable": false, "required": true}, "email": {"type": "email", "minLength": 6, "configurable": false, "required": true}, "provider": {"type": "string", "configurable": false}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true, "searchable": false}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmationToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": false, "configurable": false}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "phone": {"type": "string"}, "avatar": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "company": {"type": "string"}, "jobTitle": {"type": "string"}, "bio": {"type": "text"}, "website": {"type": "string"}, "socialMedia": {"type": "json"}, "isAgent": {"type": "boolean", "default": false}, "licenseNumber": {"type": "string"}, "properties": {"type": "relation", "relation": "oneToMany", "target": "api::property.property", "mappedBy": "owner"}, "sentMessages": {"type": "relation", "relation": "oneToMany", "target": "api::message.message", "mappedBy": "sender"}, "receivedMessages": {"type": "relation", "relation": "oneToMany", "target": "api::message.message", "mappedBy": "recipient"}, "membership": {"type": "relation", "relation": "manyToOne", "target": "api::membership.membership", "inversedBy": "users"}, "membershipStartDate": {"type": "datetime"}, "membershipEndDate": {"type": "datetime"}}}