{"version": 3, "sources": ["../../../@strapi/plugin-cloud/dist/admin/translations/uk.json.mjs"], "sourcesContent": ["var uk = {\n    \"Plugin.name\": \"Розгортання\",\n    \"Homepage.title\": \"Повністю кероване хмарне хостингування для вашого проекту Strapi\",\n    \"Homepage.subTitle\": \"Дотримуйтесь цього процесу з 2 кроків, щоб отримати все необхідне для запуску Strapi на Продукційному середовищі.\",\n    \"Homepage.githubBox.title.versioned\": \"Проект завантажено на GitHub\",\n    \"Homepage.githubBox.title.not-versioned\": \"Завантажте свій проект на GitHub\",\n    \"Homepage.githubBox.subTitle.versioned\": \"Ви це зробили! Ви на один крок ближче до того, щоб ваш проект був розміщений онлайн.\",\n    \"Homepage.githubBox.subTitle.not-versioned\": \"Ваш проект має бути версійований на GitHub перед розгортанням на Strapi Cloud.\",\n    \"Homepage.githubBox.buttonText\": \"Завантажити на GitHub\",\n    \"Homepage.cloudBox.title\": \"Розгорнути на Strapi Cloud\",\n    \"Homepage.cloudBox.subTitle\": \"Насолоджуйтесь оптимізованим стеком Strapi, включаючи базу даних, постачальника електронної пошти та CDN.\",\n    \"Homepage.cloudBox.buttonText\": \"Розгорнути на Strapi Cloud\",\n    \"Homepage.textBox.label.versioned\": \"Спробуйте Strapi Cloud безкоштовно!\",\n    \"Homepage.textBox.label.not-versioned\": \"Чому завантажувати мій проект на GitHub?\",\n    \"Homepage.textBox.text.versioned\": \"Strapi Cloud пропонує 14-денну безкоштовну пробну версію, щоб ви могли експериментувати зі своїм проектом у хмарі, включаючи всі функції.\",\n    \"Homepage.textBox.text.not-versioned\": \"Strapi Cloud буде отримувати та розгортати ваш проект з вашого репозиторію GitHub. Це найкращий спосіб версіонувати, керувати та розгортати ваш проект. Дотримуйтесь кроків на GitHub, щоб успішно завантажити його.\"\n};\n\nexport { uk as default };\n//# sourceMappingURL=uk.json.mjs.map\n"], "mappings": ";;;AAAA,IAAI,KAAK;AAAA,EACL,eAAe;AAAA,EACf,kBAAkB;AAAA,EAClB,qBAAqB;AAAA,EACrB,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,2BAA2B;AAAA,EAC3B,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,mCAAmC;AAAA,EACnC,uCAAuC;AAC3C;", "names": []}