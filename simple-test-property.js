// Simple script to test property creation with minimal data
const axios = require('axios');

const API_BASE = 'http://localhost:1337/api';

// Existing user 'badr' login credentials
const testUser = {
  email: '<EMAIL>',
  password: 'Mb123321'
};

// Test property with additional fields
const testProperty = {
  title: 'Test Property with More Fields',
  description: 'A test property with more fields',
  price: 100000,
  propertyType: 'apartment',
  area: 1000,
  address: '123 Test Street',
  city: 'Test City',
  country: 'USA',
  bedrooms: 2,
  bathrooms: 2,
  features: ['balcony', 'parking'],
  neighborhood: 'Downtown'
};

async function createTestProperty() {
  try {
    console.log('🚀 Starting simple property test...');
    
    // Step 1: Login
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/local`, {
      identifier: testUser.email,
      password: testUser.password
    });
    
    const userId = loginResponse.data.user.id;
    const userToken = loginResponse.data.jwt;
    console.log(`✅ Logged in as user ID: ${userId}`);
    
    // Step 2: Try creating property with minimal data
    console.log('🏠 Creating test property...');
    console.log('Property data:', JSON.stringify(testProperty, null, 2));
    
    try {
      const propertyResponse = await axios.post(`${API_BASE}/properties`, {
        data: testProperty
      }, {
        headers: {
          Authorization: `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Property created successfully!');
      console.log('Property ID:', propertyResponse.data.data.id);
      console.log('Property details:', JSON.stringify(propertyResponse.data.data, null, 2));
      
    } catch (error) {
      console.error('❌ Failed to create property');
      console.error('Status:', error.response?.status);
      console.error('Error details:', JSON.stringify(error.response?.data, null, 2));
      
      // Try to get more details from the backend logs
      if (error.response?.status === 500) {
        console.log('\n🔍 This is a 500 error - check the backend console for detailed error logs');
      }
    }
    
  } catch (error) {
    console.error('❌ Script failed:', error.message);
  }
}

// Run the test
createTestProperty();
