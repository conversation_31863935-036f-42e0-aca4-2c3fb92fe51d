'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import Layout from '@/components/Layout/Layout';
import { propertiesAPI } from '@/lib/api';
import { MapPin, Bed, Bath, Square, Eye, Search, Filter, Star, ChevronLeft, ChevronRight, Grid, List, SlidersHorizontal } from 'lucide-react';

interface Property {
  documentId: string;
  id: number;
  title: string;
  description: string;
  price: number;
  currency: string;
  propertyType: string;
  offer: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  areaUnit: string;
  address: string;
  city: string;
  country: string;
  neighborhood?: string;
  propertyCode?: string;
  isLuxury?: boolean;
  features?: string[];
  views: number;
  images?: any[];
  createdAt: string;
  updatedAt: string;
  slug?: string;
}

const PropertiesPageContent: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [properties, setProperties] = useState<Property[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [cities, setCities] = useState<string[]>([]);
  const [neighborhoods, setNeighborhoods] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 12,
    total: 0,
    pageCount: 0,
  });

  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    propertyType: searchParams.get('propertyType') || '',
    status: searchParams.get('status') || '',
    city: searchParams.get('city') || '',
    neighborhood: searchParams.get('neighborhood') || '',
    minPrice: searchParams.get('minPrice') || '',
    maxPrice: searchParams.get('maxPrice') || '',
    bedrooms: searchParams.get('bedrooms') || '',
    bathrooms: searchParams.get('bathrooms') || '',
    minArea: searchParams.get('minArea') || '',
    maxArea: searchParams.get('maxArea') || '',
    propertyCode: searchParams.get('propertyCode') || '',
    isLuxury: searchParams.get('isLuxury') === 'true',
    features: searchParams.get('features')?.split(',').filter(Boolean) || [],
  });

  useEffect(() => {
    fetchCities();
    fetchProperties();
  }, []);

  useEffect(() => {
    if (filters.city) {
      fetchNeighborhoods(filters.city);
    } else {
      setNeighborhoods([]);
    }
  }, [filters.city]);

  useEffect(() => {
    fetchProperties();
  }, [pagination.page]);

  const fetchCities = async () => {
    try {
      const cities = await propertiesAPI.getCities();
      setCities(cities);
    } catch (err) {
      console.error('Error fetching cities:', err);
    }
  };

  const fetchNeighborhoods = async (city: string) => {
    try {
      const neighborhoods = await propertiesAPI.getNeighborhoods(city);
      setNeighborhoods(neighborhoods);
    } catch (err) {
      console.error('Error fetching neighborhoods:', err);
    }
  };

  const fetchProperties = async () => {
    try {
      setLoading(true);
      const searchFilters = {
        ...filters,
        page: pagination.page,
        pageSize: pagination.pageSize,
      };

      const response = await propertiesAPI.search(searchFilters);
      setProperties(response.data || []);
      setPagination(prev => ({
        ...prev,
        total: response.meta?.pagination?.total || 0,
        pageCount: response.meta?.pagination?.pageCount || 0,
      }));
    } catch (err: any) {
      setError('Failed to fetch properties');
      console.error('Error fetching properties:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchProperties();

    // Update URL with search parameters
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== '' && value !== false) {
        if (Array.isArray(value)) {
          if (value.length > 0) params.set(key, value.join(','));
        } else {
          params.set(key, value.toString());
        }
      }
    });

    const newUrl = `/properties${params.toString() ? `?${params.toString()}` : ''}`;
    router.push(newUrl, { scroll: false });
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      propertyType: '',
      status: '',
      city: '',
      neighborhood: '',
      minPrice: '',
      maxPrice: '',
      bedrooms: '',
      bathrooms: '',
      minArea: '',
      maxArea: '',
      propertyCode: '',
      isLuxury: false,
      features: [],
    });
    setPagination(prev => ({ ...prev, page: 1 }));
    router.push('/properties', { scroll: false });
  };

  const getImageUrl = (property: Property) => {
    if (property.images && property.images.length > 0) {
      const image = property.images[0];
      if (image.url) {
        return image.url.startsWith('http') ? image.url : `http://localhost:1337${image.url}`;
      }
    }
    return '/api/placeholder/400/300';
  };

  const formatPrice = (price: number, currency: string) => {
    return `${currency} ${price.toLocaleString()}`;
  };

  if (loading && properties.length === 0) {
    return (
      <Layout>
        <div className="min-h-screen bg-gray-50">
          <div className="bg-white shadow">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
              <h1 className="text-3xl font-bold text-gray-900">Properties</h1>
              <p className="text-gray-600 mt-2">Find your perfect property</p>
            </div>
          </div>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="bg-white rounded-lg shadow-lg overflow-hidden animate-pulse">
                  <div className="h-48 bg-gray-300"></div>
                  <div className="p-6">
                    <div className="h-6 bg-gray-300 rounded mb-2"></div>
                    <div className="h-4 bg-gray-300 rounded mb-3"></div>
                    <div className="flex space-x-4 mb-4">
                      <div className="h-4 bg-gray-300 rounded w-16"></div>
                      <div className="h-4 bg-gray-300 rounded w-16"></div>
                      <div className="h-4 bg-gray-300 rounded w-16"></div>
                    </div>
                    <div className="h-8 bg-gray-300 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Properties</h1>
                <p className="text-gray-600 mt-2">
                  {pagination.total > 0
                    ? `${pagination.total} properties found`
                    : 'Find your perfect property'
                  }
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center bg-gray-100 rounded-lg p-1">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded ${viewMode === 'grid' ? 'bg-white shadow' : ''}`}
                  >
                    <Grid className="h-5 w-5" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded ${viewMode === 'list' ? 'bg-white shadow' : ''}`}
                  >
                    <List className="h-5 w-5" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Search and Filters */}
          <div className="bg-white rounded-lg shadow p-6 mb-8">
            <form onSubmit={handleSearch} className="space-y-6">
              {/* Basic Filters */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="lg:col-span-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search by title, location, or property code..."
                      value={filters.search}
                      onChange={(e) => handleFilterChange('search', e.target.value)}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <select
                  value={filters.propertyType}
                  onChange={(e) => handleFilterChange('propertyType', e.target.value)}
                  className="px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Property Types</option>
                  <option value="apartment">Apartment</option>
                  <option value="villa">Villa</option>
                  <option value="townhouse">Townhouse</option>
                  <option value="penthouse">Penthouse</option>
                  <option value="studio">Studio</option>
                  <option value="duplex">Duplex</option>
                  <option value="land">Land</option>
                  <option value="commercial">Commercial</option>
                </select>

                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Offer Types</option>
                  <option value="for-sale">For Sale</option>
                  <option value="for-rent">For Rent</option>
                  <option value="sold">Sold</option>
                  <option value="rented">Rented</option>
                  <option value="off-market">Off Market</option>
                </select>
              </div>

              {/* Advanced Filters Toggle */}
              <div className="flex items-center justify-between">
                <button
                  type="button"
                  onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  className="flex items-center text-blue-600 hover:text-blue-700 transition-colors"
                >
                  <SlidersHorizontal className="h-5 w-5 mr-2" />
                  {showAdvancedFilters ? 'Hide' : 'Show'} Advanced Filters
                </button>

                <div className="flex items-center space-x-4">
                  <button
                    type="button"
                    onClick={clearFilters}
                    className="text-gray-600 hover:text-gray-700 transition-colors"
                  >
                    Clear All
                  </button>
                  <button
                    type="submit"
                    className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors flex items-center"
                  >
                    <Filter className="h-5 w-5 mr-2" />
                    Search Properties
                  </button>
                </div>
              </div>

              {/* Advanced Filters */}
              {showAdvancedFilters && (
                <div className="border-t pt-6 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <select
                      value={filters.city}
                      onChange={(e) => handleFilterChange('city', e.target.value)}
                      className="px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">All Cities</option>
                      {cities.map(city => (
                        <option key={city} value={city}>{city}</option>
                      ))}
                    </select>

                    <select
                      value={filters.neighborhood}
                      onChange={(e) => handleFilterChange('neighborhood', e.target.value)}
                      className="px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      disabled={!filters.city}
                    >
                      <option value="">All Neighborhoods</option>
                      {neighborhoods.map(neighborhood => (
                        <option key={neighborhood} value={neighborhood}>{neighborhood}</option>
                      ))}
                    </select>

                    <select
                      value={filters.bedrooms}
                      onChange={(e) => handleFilterChange('bedrooms', e.target.value)}
                      className="px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Any Bedrooms</option>
                      <option value="1">1 Bedroom</option>
                      <option value="2">2 Bedrooms</option>
                      <option value="3">3 Bedrooms</option>
                      <option value="4">4 Bedrooms</option>
                      <option value="5">5+ Bedrooms</option>
                    </select>

                    <select
                      value={filters.bathrooms}
                      onChange={(e) => handleFilterChange('bathrooms', e.target.value)}
                      className="px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="">Any Bathrooms</option>
                      <option value="1">1 Bathroom</option>
                      <option value="2">2 Bathrooms</option>
                      <option value="3">3 Bathrooms</option>
                      <option value="4">4+ Bathrooms</option>
                    </select>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <input
                      type="number"
                      placeholder="Min Price"
                      value={filters.minPrice}
                      onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                      className="px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />

                    <input
                      type="number"
                      placeholder="Max Price"
                      value={filters.maxPrice}
                      onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                      className="px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />

                    <input
                      type="number"
                      placeholder="Min Area (sq ft)"
                      value={filters.minArea}
                      onChange={(e) => handleFilterChange('minArea', e.target.value)}
                      className="px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />

                    <input
                      type="number"
                      placeholder="Max Area (sq ft)"
                      value={filters.maxArea}
                      onChange={(e) => handleFilterChange('maxArea', e.target.value)}
                      className="px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <input
                      type="text"
                      placeholder="Property Code"
                      value={filters.propertyCode}
                      onChange={(e) => handleFilterChange('propertyCode', e.target.value)}
                      className="px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />

                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="luxury"
                        checked={filters.isLuxury}
                        onChange={(e) => handleFilterChange('isLuxury', e.target.checked)}
                        className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="luxury" className="ml-3 text-gray-700 font-medium">
                        Luxury Properties Only
                      </label>
                    </div>
                  </div>
                </div>
              )}
            </form>
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md mb-8">
              {error}
            </div>
          )}

          {/* Properties Display */}
          {!loading && properties.length === 0 ? (
            <div className="text-center py-12">
              <div className="max-w-md mx-auto">
                <div className="mb-4">
                  <Search className="h-16 w-16 text-gray-300 mx-auto" />
                </div>
                <p className="text-gray-500 text-lg mb-2">No properties found</p>
                <p className="text-gray-400 mb-4">Try adjusting your search criteria or clear all filters</p>
                <button
                  onClick={clearFilters}
                  className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          ) : (
            <>
              {/* Properties Grid/List */}
              <div className={viewMode === 'grid'
                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
                : "space-y-6"
              }>
                {properties.map((property) => (
                  <div
                    key={property.documentId}
                    className={`bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 ${
                      viewMode === 'list' ? 'flex' : ''
                    }`}
                  >
                    <div className={`relative ${viewMode === 'list' ? 'w-80 flex-shrink-0' : ''}`}>
                      <img
                        src={getImageUrl(property)}
                        alt={property.title}
                        className={`object-cover ${viewMode === 'list' ? 'w-full h-full' : 'w-full h-48'}`}
                      />
                      <div className="absolute top-4 left-4 flex gap-2">
                        <span className={`text-white px-3 py-1 rounded-full text-sm font-semibold capitalize ${
                          property.offer === 'for-sale' ? 'bg-green-600' :
                          property.offer === 'for-rent' ? 'bg-blue-600' :
                          property.offer === 'sold' ? 'bg-gray-600' :
                          property.offer === 'rented' ? 'bg-purple-600' :
                          'bg-orange-600'
                        }`}>
                          {property.offer.replace('-', ' ')}
                        </span>
                        {property.isLuxury && (
                          <span className="bg-yellow-600 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center">
                            <Star className="h-3 w-3 mr-1" />
                            Luxury
                          </span>
                        )}
                      </div>
                      <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-2 py-1 rounded flex items-center">
                        <Eye className="h-4 w-4 mr-1" />
                        <span className="text-sm">{property.views || 0}</span>
                      </div>
                      {property.propertyCode && (
                        <div className="absolute bottom-4 left-4">
                          <span className="bg-white/90 text-gray-800 px-2 py-1 rounded text-xs font-semibold">
                            #{property.propertyCode}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className={`p-6 ${viewMode === 'list' ? 'flex-1' : ''}`}>
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-xl font-semibold text-gray-900 line-clamp-2">
                          {property.title}
                        </h3>
                        <span className="text-sm text-gray-500 capitalize bg-gray-100 px-2 py-1 rounded ml-2">
                          {property.propertyType.replace('-', ' ')}
                        </span>
                      </div>

                      <div className="flex items-center text-gray-600 mb-3">
                        <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                        <span className="text-sm line-clamp-1">
                          {property.neighborhood ? `${property.neighborhood}, ` : ''}{property.city}, {property.country}
                        </span>
                      </div>

                      {property.description && viewMode === 'list' && (
                        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                          {property.description}
                        </p>
                      )}

                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          {property.bedrooms > 0 && (
                            <div className="flex items-center">
                              <Bed className="h-4 w-4 mr-1" />
                              <span>{property.bedrooms}</span>
                            </div>
                          )}
                          {property.bathrooms > 0 && (
                            <div className="flex items-center">
                              <Bath className="h-4 w-4 mr-1" />
                              <span>{property.bathrooms}</span>
                            </div>
                          )}
                          {property.area > 0 && (
                            <div className="flex items-center">
                              <Square className="h-4 w-4 mr-1" />
                              <span>{property.area} {property.areaUnit || 'sq ft'}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="text-2xl font-bold text-blue-600">
                          {formatPrice(property.price, property.currency)}
                        </div>
                        <button
                          onClick={() => router.push(`/properties/${property.documentId}`)}
                          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-semibold"
                        >
                          View Details
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {pagination.pageCount > 1 && (
                <div className="flex items-center justify-between mt-12 bg-white rounded-lg shadow p-6">
                  <div className="text-sm text-gray-600">
                    Showing {((pagination.page - 1) * pagination.pageSize) + 1} to{' '}
                    {Math.min(pagination.page * pagination.pageSize, pagination.total)} of{' '}
                    {pagination.total} properties
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                      disabled={pagination.page === 1}
                      className="flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ChevronLeft className="h-4 w-4 mr-1" />
                      Previous
                    </button>

                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, pagination.pageCount) }, (_, i) => {
                        const pageNum = i + 1;
                        return (
                          <button
                            key={pageNum}
                            onClick={() => setPagination(prev => ({ ...prev, page: pageNum }))}
                            className={`px-3 py-2 text-sm font-medium rounded-md ${
                              pagination.page === pageNum
                                ? 'bg-blue-600 text-white'
                                : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                            }`}
                          >
                            {pageNum}
                          </button>
                        );
                      })}
                    </div>

                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                      disabled={pagination.page === pagination.pageCount}
                      className="flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </Layout>
  );
};

const PropertiesPage: React.FC = () => {
  return (
    <Suspense fallback={
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
        </div>
      </Layout>
    }>
      <PropertiesPageContent />
    </Suspense>
  );
};

export default PropertiesPage;
