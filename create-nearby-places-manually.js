// Manual creation of nearby place categories
const axios = require('axios');

const API_BASE = 'http://localhost:1337/api';

// Test user credentials
const userCredentials = {
  email: '<EMAIL>',
  password: 'Mb123321'
};

// Simplified categories for manual creation
const categories = [
  {
    name: 'education',
    displayName: 'Education',
    description: 'Schools, universities, libraries, and educational institutions',
    icon: '🎓',
    googlePlaceTypes: ['school', 'primary_school', 'secondary_school', 'university', 'library'],
    enabled: true,
    searchRadius: 2000,
    maxResults: 10,
    priority: 10,
    color: '#10B981'
  },
  {
    name: 'restaurants',
    displayName: 'Restaurants & Food',
    description: 'Restaurants, cafes, bars, and food establishments',
    icon: '🍽️',
    googlePlaceTypes: ['restaurant', 'cafe', 'bar', 'bakery', 'meal_delivery', 'meal_takeaway'],
    enabled: true,
    searchRadius: 1000,
    maxResults: 15,
    priority: 9,
    color: '#F59E0B'
  },
  {
    name: 'shopping',
    displayName: 'Shopping',
    description: 'Shopping malls, stores, supermarkets, and retail',
    icon: '🛍️',
    googlePlaceTypes: ['shopping_mall', 'supermarket', 'convenience_store', 'department_store', 'clothing_store', 'electronics_store', 'store'],
    enabled: true,
    searchRadius: 1500,
    maxResults: 10,
    priority: 8,
    color: '#8B5CF6'
  },
  {
    name: 'healthcare',
    displayName: 'Healthcare',
    description: 'Hospitals, clinics, pharmacies, and medical facilities',
    icon: '🏥',
    googlePlaceTypes: ['hospital', 'doctor', 'dentist', 'pharmacy', 'physiotherapist', 'veterinary_care'],
    enabled: true,
    searchRadius: 2000,
    maxResults: 8,
    priority: 9,
    color: '#EF4444'
  },
  {
    name: 'transportation',
    displayName: 'Transportation',
    description: 'Bus stops, train stations, airports, and transit hubs',
    icon: '🚌',
    googlePlaceTypes: ['bus_station', 'train_station', 'subway_station', 'light_rail_station', 'transit_station', 'taxi_stand', 'airport', 'gas_station'],
    enabled: true,
    searchRadius: 2000,
    maxResults: 8,
    priority: 7,
    color: '#3B82F6'
  }
];

async function createCategoriesManually() {
  console.log('🚀 Manual Nearby Places Setup...\n');

  try {
    // Step 1: Login
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post(`${API_BASE}/auth/local`, {
      identifier: userCredentials.email,
      password: userCredentials.password
    });
    const token = loginResponse.data.jwt;
    console.log('✅ Login successful');

    // Step 2: Check if endpoint exists
    console.log('\n🔍 Checking API endpoint...');
    try {
      const testResponse = await axios.get(`${API_BASE}/nearby-place-categories`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ API endpoint accessible');
    } catch (error) {
      console.log(`❌ API endpoint error: ${error.response?.status}`);
      
      if (error.response?.status === 404) {
        console.log('\n📋 SOLUTION: Create the content type manually in Strapi admin:');
        console.log('1. Go to http://localhost:1337/admin');
        console.log('2. Navigate to Content-Type Builder');
        console.log('3. Create Collection Type: "Nearby Place Category"');
        console.log('4. Add these fields:');
        console.log('   - name (Text, Required, Unique)');
        console.log('   - displayName (Text, Required)');
        console.log('   - description (Long Text)');
        console.log('   - icon (Text, Required)');
        console.log('   - googlePlaceTypes (JSON, Required)');
        console.log('   - enabled (Boolean, Default: true)');
        console.log('   - searchRadius (Number, Default: 1000, Min: 100, Max: 5000)');
        console.log('   - maxResults (Number, Default: 10, Min: 1, Max: 20)');
        console.log('   - priority (Number, Default: 0)');
        console.log('   - color (Text, Default: "#3B82F6")');
        console.log('5. Save and restart Strapi');
        console.log('6. Configure permissions for Authenticated users');
        console.log('7. Run this script again');
        return;
      }
      
      if (error.response?.status === 403) {
        console.log('\n🔒 SOLUTION: Configure permissions:');
        console.log('1. Go to http://localhost:1337/admin');
        console.log('2. Settings > Users & Permissions Plugin > Roles');
        console.log('3. Edit "Authenticated" role');
        console.log('4. Find "Nearby-place-category" and enable permissions');
        console.log('5. Save and run this script again');
        return;
      }
    }

    // Step 3: Create categories
    console.log('\n📋 Creating categories...');
    for (const category of categories) {
      try {
        await axios.post(`${API_BASE}/nearby-place-categories`, {
          data: category
        }, {
          headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });
        console.log(`✅ Created: ${category.displayName}`);
      } catch (error) {
        if (error.response?.data?.error?.message?.includes('unique')) {
          console.log(`⚠️  Already exists: ${category.displayName}`);
        } else {
          console.log(`❌ Failed: ${category.displayName} - ${error.response?.data?.error?.message || error.message}`);
        }
      }
    }

    console.log('\n🎉 Setup complete!');

  } catch (error) {
    console.error('❌ Setup failed:', error.message);
  }
}

// Instructions for manual setup
function showManualInstructions() {
  console.log('\n📝 MANUAL SETUP INSTRUCTIONS:');
  console.log('\n1. Create Content Type in Strapi Admin:');
  console.log('   - Go to http://localhost:1337/admin');
  console.log('   - Content-Type Builder > Create new collection type');
  console.log('   - Name: "Nearby Place Category"');
  console.log('   - API ID: "nearby-place-category"');
  
  console.log('\n2. Add these fields:');
  categories[0] && Object.keys(categories[0]).forEach(field => {
    const value = categories[0][field];
    const type = Array.isArray(value) ? 'JSON' : typeof value === 'boolean' ? 'Boolean' : typeof value === 'number' ? 'Number' : 'Text';
    console.log(`   - ${field}: ${type}${field === 'name' ? ' (Required, Unique)' : field === 'displayName' || field === 'icon' || field === 'googlePlaceTypes' ? ' (Required)' : ''}`);
  });
  
  console.log('\n3. Save and restart Strapi');
  console.log('\n4. Configure permissions:');
  console.log('   - Settings > Users & Permissions Plugin > Roles');
  console.log('   - Edit "Authenticated" role');
  console.log('   - Enable nearby-place-category permissions');
  
  console.log('\n5. Create categories manually or run this script');
  
  console.log('\n📋 Categories to create:');
  categories.forEach((cat, i) => {
    console.log(`\n${i + 1}. ${cat.displayName}:`);
    console.log(`   Name: ${cat.name}`);
    console.log(`   Icon: ${cat.icon}`);
    console.log(`   Google Place Types: ${JSON.stringify(cat.googlePlaceTypes)}`);
    console.log(`   Search Radius: ${cat.searchRadius}m`);
    console.log(`   Max Results: ${cat.maxResults}`);
    console.log(`   Priority: ${cat.priority}`);
    console.log(`   Color: ${cat.color}`);
    console.log(`   Enabled: ${cat.enabled}`);
  });
}

// Run setup or show instructions
createCategoriesManually().catch(() => {
  showManualInstructions();
});
